# FSA 账户批量创建 - 快速安装指南

## 🚀 5分钟快速上手

### 第一步：安装油猴扩展
1. **Chrome浏览器**：访问 [Chrome网上应用店](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo) 安装 Tampermonkey
2. **Edge浏览器**：访问 [Edge扩展商店](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd) 安装 Tampermonkey
3. **Firefox浏览器**：访问 [Firefox扩展商店](https://addons.mozilla.org/zh-CN/firefox/addon/tampermonkey/) 安装 Tampermonkey

### 第二步：安装脚本
1. 点击浏览器右上角的 **Tampermonkey 图标**
2. 选择 **"管理面板"**
3. 点击 **"+"** 按钮创建新脚本
4. **删除所有默认内容**
5. **复制粘贴** `FSA_Account_Creator.user.js` 或 `FSA_Simple_Demo.user.js` 的全部内容
6. 按 **Ctrl+S** 保存脚本

### 第三步：使用脚本
1. 访问 https://studentaid.gov/fsa-id/create-account/personal-info
2. 页面右上角会出现控制面板
3. 点击 **"开始处理"** 或 **"填写当前"** 按钮

## 📋 两个版本对比

### 完整版 (FSA_Account_Creator.user.js)
- ✅ 完整的自动化流程
- ✅ 自动模式和手动模式
- ✅ 详细的日志记录
- ✅ 错误处理和恢复
- ✅ 进度跟踪
- ✅ 日志导出功能
- 🎯 **推荐用于正式批量处理**

### 简化版 (FSA_Simple_Demo.user.js)
- ✅ 简单易用的界面
- ✅ 基本的表单填写
- ✅ 学生信息切换
- ✅ 错误检测
- 🎯 **推荐用于测试和学习**

## 🔧 学生数据配置

### 在完整版中修改数据
找到这部分代码并修改：
```javascript
const studentData = [
    ['STEPHANIE', 'A', 'STILLINGS', '07/18/1990', '*********'],
    ['AARON', 'D', 'CORNETT', '01/18/1990', '*********'],
    ['MORGAN', 'C', 'STEPHENS', '04/24/1997', '*********']
    // 添加更多学生数据...
];
```

### 在简化版中修改数据
找到这部分代码并修改：
```javascript
const students = [
    {
        firstName: 'STEPHANIE',
        middleInitial: 'A',
        lastName: 'STILLINGS',
        birthDate: '07/18/1990',
        ssn: '*********'
    },
    // 添加更多学生对象...
];
```

## ⚡ 使用技巧

### 自动模式使用
1. 勾选 **"自动模式"** 复选框
2. 点击 **"开始处理"**
3. 脚本会自动处理所有学生，无需人工干预

### 手动模式使用
1. 点击 **"开始处理"** 填写当前学生
2. 检查填写结果
3. 手动点击页面的 **"Continue"** 按钮
4. 点击 **"下一个"** 处理下一个学生

### 处理已存在账户
- 脚本会自动检测 "Account already exists" 错误
- 自动模式：自动跳过并处理下一个学生
- 手动模式：提示用户选择是否跳过

## 🛠️ 故障排除

### 脚本没有显示控制面板
1. 检查 Tampermonkey 是否已启用
2. 确认脚本已安装并启用
3. 刷新页面重试
4. 检查网址是否匹配 `https://studentaid.gov/fsa-id/create-account/*`

### 表单填写失败
1. 打开浏览器开发者工具 (F12)
2. 查看 Console 标签页的错误信息
3. 检查页面结构是否发生变化
4. 尝试手动填写测试

### Continue 按钮无法点击
1. 检查所有必填字段是否已填写
2. 查看是否有验证错误提示
3. 手动点击 Continue 按钮继续

## 📊 处理结果

### 成功情况
- 表单填写完成
- 自动点击 Continue 按钮
- 进入下一步流程

### 账户已存在
- 显示警告信息
- 记录到日志中
- 自动或手动跳过

### 填写失败
- 显示错误信息
- 提供重试选项
- 记录详细日志

## 🔒 安全提醒

1. **数据安全**：所有处理都在本地浏览器中进行
2. **权限确认**：确保有权限处理这些学生信息
3. **合规使用**：仅用于合法的教育管理目的
4. **数据清理**：处理完成后建议清除浏览器数据

## 📞 技术支持

如遇问题，请提供：
- 浏览器版本和类型
- Tampermonkey 版本
- 错误截图
- 控制台错误信息
- 处理日志（如有）

---

**注意**：此脚本仅用于合法的教育管理目的，请确保遵守相关法律法规和隐私政策。
