// ==UserScript==
// @name         FSA API 批量处理器 - 后台协议版
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  基于HTTP API的FSA账户批量创建工具，纯后台处理
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_download
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 学生数据配置
    const STUDENT_DATA = [
        {
            firstName: 'STEPHANIE',
            middleInitial: 'A',
            lastName: 'STILLINGS',
            birthMonth: '07',
            birthDay: '18',
            birthYear: '1990',
            ssn: '*********'
        },
        {
            firstName: 'AARON',
            middleInitial: 'D',
            lastName: 'CORNETT',
            birthMonth: '01',
            birthDay: '18',
            birthYear: '1990',
            ssn: '*********'
        },
        {
            firstName: 'MORGAN',
            middleInitial: 'C',
            lastName: 'STEPHENS',
            birthMonth: '04',
            birthDay: '24',
            birthYear: '1997',
            ssn: '*********'
        }
    ];

    let currentIndex = 0;
    let processingResults = [];
    let isProcessing = false;

    // API处理器类
    class FSAAPIProcessor {
        constructor() {
            this.baseURL = 'https://studentaid.gov';
            this.sessionCookies = '';
        }

        // 生成用户名
        generateUsername(firstName, lastName, ssn) {
            const base = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`;
            const suffix = ssn.slice(-4);
            return `${base}${suffix}`;
        }

        // 生成邮箱
        generateEmail(firstName, lastName, ssn) {
            const username = this.generateUsername(firstName, lastName, ssn);
            const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
            const domain = domains[Math.floor(Math.random() * domains.length)];
            return `${username}@${domain}`;
        }

        // 生成密码
        generatePassword() {
            const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const lower = 'abcdefghijklmnopqrstuvwxyz';
            const numbers = '0123456789';
            
            let password = '';
            password += upper[Math.floor(Math.random() * upper.length)];
            password += lower[Math.floor(Math.random() * lower.length)];
            password += numbers[Math.floor(Math.random() * numbers.length)];
            
            const allChars = upper + lower + numbers;
            for (let i = 0; i < 5; i++) {
                password += allChars[Math.floor(Math.random() * allChars.length)];
            }
            
            return password.split('').sort(() => Math.random() - 0.5).join('');
        }

        // 发送API请求
        async apiRequest(url, method = 'POST', data = null) {
            return new Promise((resolve, reject) => {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://studentaid.gov/fsa-id/create-account/personal-info'
                };

                GM_xmlhttpRequest({
                    method: method,
                    url: url,
                    headers: headers,
                    data: data ? JSON.stringify(data) : null,
                    timeout: 30000,
                    onload: function(response) {
                        try {
                            const result = {
                                status: response.status,
                                data: response.responseText ? JSON.parse(response.responseText) : null,
                                headers: response.responseHeaders
                            };
                            resolve(result);
                        } catch (e) {
                            resolve({
                                status: response.status,
                                data: response.responseText,
                                headers: response.responseHeaders
                            });
                        }
                    },
                    onerror: function(error) {
                        reject(error);
                    },
                    ontimeout: function() {
                        reject(new Error('请求超时'));
                    }
                });
            });
        }

        // 步骤1：检查出生日期
        async checkDateOfBirth(student) {
            const dobData = {
                month: student.birthMonth,
                day: student.birthDay,
                year: student.birthYear
            };

            try {
                const response = await this.apiRequest(
                    `${this.baseURL}/app/api/auth/registration/checkDob`,
                    'POST',
                    dobData
                );
                
                return {
                    success: response.status === 200,
                    message: response.status === 200 ? '出生日期验证通过' : `验证失败: ${response.status}`,
                    data: response.data
                };
            } catch (error) {
                return {
                    success: false,
                    message: `出生日期验证异常: ${error.message}`,
                    data: null
                };
            }
        }

        // 步骤2：提交个人信息
        async submitPersonalInfo(student) {
            const personalData = {
                firstName: student.firstName,
                middleInitial: student.middleInitial,
                lastName: student.lastName,
                birthMonth: student.birthMonth,
                birthDay: student.birthDay,
                birthYear: student.birthYear,
                ssn: student.ssn
            };

            try {
                const response = await this.apiRequest(
                    `${this.baseURL}/app/api/auth/registration/a`,
                    'POST',
                    personalData
                );

                if (response.status === 200) {
                    return {
                        success: true,
                        message: '个人信息提交成功',
                        data: response.data
                    };
                } else if (response.status === 409) {
                    return {
                        success: false,
                        message: '账户已存在',
                        data: response.data
                    };
                } else {
                    return {
                        success: false,
                        message: `个人信息提交失败: HTTP ${response.status}`,
                        data: response.data
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    message: `个人信息提交异常: ${error.message}`,
                    data: null
                };
            }
        }

        // 步骤3：提交账户信息
        async submitAccountInfo(student) {
            const username = this.generateUsername(student.firstName, student.lastName, student.ssn);
            const email = this.generateEmail(student.firstName, student.lastName, student.ssn);
            const password = this.generatePassword();

            const accountData = {
                username: username,
                email: email,
                confirmEmail: email,
                password: password,
                confirmPassword: password
            };

            try {
                const response = await this.apiRequest(
                    `${this.baseURL}/app/api/auth/registration/b`,
                    'POST',
                    accountData
                );

                if (response.status === 200) {
                    return {
                        success: true,
                        message: '账户信息提交成功',
                        data: response.data,
                        credentials: {
                            username: username,
                            email: email,
                            password: password
                        }
                    };
                } else {
                    return {
                        success: false,
                        message: `账户信息提交失败: HTTP ${response.status}`,
                        data: response.data,
                        credentials: null
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    message: `账户信息提交异常: ${error.message}`,
                    data: null,
                    credentials: null
                };
            }
        }

        // 处理单个学生
        async processSingleStudent(student) {
            const result = {
                studentName: `${student.firstName} ${student.middleInitial} ${student.lastName}`,
                ssn: student.ssn,
                startTime: new Date().toISOString(),
                steps: [],
                success: false,
                finalMessage: '',
                credentials: null
            };

            updateStatus(`正在处理: ${result.studentName}`, 'processing');

            try {
                // 步骤1：检查出生日期
                updateStatus(`${result.studentName} - 验证出生日期...`, 'processing');
                const dobResult = await this.checkDateOfBirth(student);
                result.steps.push({
                    step: 'checkDob',
                    success: dobResult.success,
                    message: dobResult.message
                });

                if (!dobResult.success) {
                    result.finalMessage = `出生日期验证失败: ${dobResult.message}`;
                    return result;
                }

                // 等待1-2秒
                await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));

                // 步骤2：提交个人信息
                updateStatus(`${result.studentName} - 提交个人信息...`, 'processing');
                const personalResult = await this.submitPersonalInfo(student);
                result.steps.push({
                    step: 'personalInfo',
                    success: personalResult.success,
                    message: personalResult.message
                });

                if (!personalResult.success) {
                    result.finalMessage = personalResult.message;
                    if (personalResult.message.includes('已存在')) {
                        result.finalMessage = '账户已存在，跳过创建';
                    }
                    return result;
                }

                // 等待2-3秒
                await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

                // 步骤3：提交账户信息
                updateStatus(`${result.studentName} - 创建账户信息...`, 'processing');
                const accountResult = await this.submitAccountInfo(student);
                result.steps.push({
                    step: 'accountInfo',
                    success: accountResult.success,
                    message: accountResult.message
                });

                if (accountResult.success) {
                    result.success = true;
                    result.finalMessage = '账户创建成功！';
                    result.credentials = accountResult.credentials;
                } else {
                    result.finalMessage = accountResult.message;
                }

            } catch (error) {
                result.finalMessage = `处理异常: ${error.message}`;
            } finally {
                result.endTime = new Date().toISOString();
            }

            return result;
        }
    }

    // 创建控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'fsa-api-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 350px;
            background: #fff;
            border: 2px solid #0066cc;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
            font-size: 14px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        panel.innerHTML = `
            <div style="background: #0066cc; color: white; padding: 8px; margin: -15px -15px 10px -15px; border-radius: 6px 6px 0 0;">
                <strong>🚀 FSA API 批量处理器</strong>
            </div>
            <div id="status">准备就绪 - 后台协议模式</div>
            <div style="margin: 10px 0;">
                当前学生: <span id="current-student">未开始</span>
            </div>
            <div style="margin: 10px 0;">
                进度: <span id="progress">0/3</span>
            </div>
            <div style="margin: 10px 0;">
                <button id="start-api-btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">开始API处理</button>
                <button id="download-results-btn" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" disabled>下载结果</button>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <div><strong>处理模式:</strong> 纯API调用</div>
                <div><strong>优势:</strong> 快速、稳定、后台运行</div>
            </div>
            <div id="results-summary" style="margin-top: 10px; font-size: 12px; display: none;">
                <div style="font-weight: bold; color: #0066cc;">处理结果摘要:</div>
                <div id="summary-content"></div>
            </div>
        `;

        document.body.appendChild(panel);
        bindEvents();
    }

    // 绑定事件
    function bindEvents() {
        document.getElementById('start-api-btn').addEventListener('click', startAPIProcessing);
        document.getElementById('download-results-btn').addEventListener('click', downloadResults);
    }

    // 更新状态
    function updateStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        const progressEl = document.getElementById('progress');
        
        if (statusEl) {
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745',
                'warning': '#ffc107',
                'error': '#dc3545',
                'processing': '#6f42c1'
            };
            
            statusEl.style.color = colors[type] || colors.info;
            statusEl.textContent = message;
        }
        
        if (progressEl) {
            progressEl.textContent = `${currentIndex}/${STUDENT_DATA.length}`;
        }
        
        console.log(`[FSA API] ${message}`);
    }

    // 开始API处理
    async function startAPIProcessing() {
        if (isProcessing) return;
        
        isProcessing = true;
        currentIndex = 0;
        processingResults = [];
        
        const startBtn = document.getElementById('start-api-btn');
        const downloadBtn = document.getElementById('download-results-btn');
        
        startBtn.disabled = true;
        startBtn.textContent = '处理中...';
        downloadBtn.disabled = true;
        
        updateStatus('初始化API处理器...', 'processing');
        
        const processor = new FSAAPIProcessor();
        
        try {
            for (let i = 0; i < STUDENT_DATA.length; i++) {
                currentIndex = i + 1;
                const student = STUDENT_DATA[i];
                
                document.getElementById('current-student').textContent = 
                    `${student.firstName} ${student.lastName}`;
                
                updateStatus(`处理学生 ${currentIndex}/${STUDENT_DATA.length}`, 'processing');
                
                const result = await processor.processSingleStudent(student);
                processingResults.push(result);
                
                // 更新结果摘要
                updateResultsSummary();
                
                if (result.success) {
                    updateStatus(`✅ ${result.studentName} - 处理成功`, 'success');
                } else {
                    updateStatus(`❌ ${result.studentName} - ${result.finalMessage}`, 'error');
                }
                
                // 添加延迟
                if (i < STUDENT_DATA.length - 1) {
                    const delay = 2000 + Math.random() * 3000;
                    updateStatus(`等待 ${Math.round(delay/1000)} 秒后处理下一个...`, 'info');
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
            
            updateStatus('🎉 所有学生处理完成！', 'success');
            
        } catch (error) {
            updateStatus(`❌ 处理异常: ${error.message}`, 'error');
        } finally {
            isProcessing = false;
            startBtn.disabled = false;
            startBtn.textContent = '重新处理';
            downloadBtn.disabled = false;
        }
    }

    // 更新结果摘要
    function updateResultsSummary() {
        const summaryDiv = document.getElementById('results-summary');
        const contentDiv = document.getElementById('summary-content');
        
        if (processingResults.length === 0) {
            summaryDiv.style.display = 'none';
            return;
        }
        
        summaryDiv.style.display = 'block';
        
        const successful = processingResults.filter(r => r.success).length;
        const failed = processingResults.length - successful;
        
        let content = `
            <div>✅ 成功: ${successful} | ❌ 失败: ${failed}</div>
        `;
        
        processingResults.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            content += `<div style="margin-top: 5px;">${status} ${result.studentName}</div>`;
            
            if (result.success && result.credentials) {
                content += `<div style="margin-left: 15px; font-size: 11px; color: #666;">
                    用户名: ${result.credentials.username}<br>
                    邮箱: ${result.credentials.email}<br>
                    密码: ${result.credentials.password}
                </div>`;
            }
        });
        
        contentDiv.innerHTML = content;
    }

    // 下载结果
    function downloadResults() {
        if (processingResults.length === 0) {
            alert('没有处理结果可下载');
            return;
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const report = generateDetailedReport();
        
        // 下载文本报告
        const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        GM_download(url, `FSA_API_处理报告_${timestamp}.txt`, url);
        
        // 下载JSON数据
        const jsonData = JSON.stringify(processingResults, null, 2);
        const jsonBlob = new Blob([jsonData], { type: 'application/json;charset=utf-8' });
        const jsonUrl = URL.createObjectURL(jsonBlob);
        
        GM_download(jsonUrl, `FSA_API_数据_${timestamp}.json`, jsonUrl);
        
        updateStatus('📁 结果文件已下载', 'success');
    }

    // 生成详细报告
    function generateDetailedReport() {
        const successful = processingResults.filter(r => r.success).length;
        const total = processingResults.length;
        
        let report = `
=== FSA API 批量处理报告 ===
处理时间: ${new Date().toLocaleString()}
处理模式: 纯API调用（后台协议）
总计学生: ${total}
成功创建: ${successful}
失败/跳过: ${total - successful}
成功率: ${(successful/total*100).toFixed(1)}%

=== 详细结果 ===
`;
        
        processingResults.forEach((result, index) => {
            report += `
${index + 1}. ${result.studentName} (SSN: ${result.ssn})
   状态: ${result.success ? '✅ 成功' : '❌ 失败'}
   最终信息: ${result.finalMessage}
`;
            
            if (result.success && result.credentials) {
                report += `   账户信息:
     用户名: ${result.credentials.username}
     邮箱: ${result.credentials.email}
     密码: ${result.credentials.password}
`;
            }
            
            report += `   处理步骤:
`;
            result.steps.forEach(step => {
                const status = step.success ? '✅' : '❌';
                report += `     ${step.step}: ${status} ${step.message}
`;
            });
        });
        
        return report;
    }

    // 初始化
    function init() {
        setTimeout(() => {
            createControlPanel();
            updateStatus('FSA API 处理器已加载 - 点击开始处理', 'success');
            console.log('FSA API 批量处理器已启动');
            console.log('学生数据:', STUDENT_DATA);
        }, 1000);
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
