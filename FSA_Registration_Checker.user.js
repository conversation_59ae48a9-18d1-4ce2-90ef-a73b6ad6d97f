// ==UserScript==
// @name         FSA 注册状态检查器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  批量检查FSA账户是否已被注册
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_xmlhttpRequest
// @grant        GM_download
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 学生数据 - 只需要检查注册状态
    const STUDENTS = [
        { name: 'STEPHANIE', mi: 'A', last: 'STILLINGS', dob: '07/18/1990', ssn: '*********' },
        { name: 'AARON', mi: 'D', last: 'CORNETT', dob: '01/18/1990', ssn: '*********' },
        { name: 'MORGAN', mi: 'C', last: 'STEPHENS', dob: '04/24/1997', ssn: '*********' }
    ];

    let checkResults = [];
    let isChecking = false;

    // API调用函数
    function apiCall(endpoint, data) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `https://studentaid.gov/app/api/auth/registration/${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://studentaid.gov/fsa-id/create-account/personal-info'
                },
                data: JSON.stringify(data),
                timeout: 20000,
                onload: (response) => {
                    resolve({
                        status: response.status,
                        statusText: response.statusText,
                        data: response.responseText
                    });
                },
                onerror: reject,
                ontimeout: () => reject(new Error('请求超时'))
            });
        });
    }

    // 检查单个学生的注册状态
    async function checkStudentRegistration(student) {
        const [month, day, year] = student.dob.split('/');
        const fullName = `${student.name} ${student.mi} ${student.last}`;
        
        console.log(`🔍 检查: ${fullName} (SSN: ${student.ssn})`);
        updateStatus(`检查: ${fullName}`);

        const result = {
            name: fullName,
            ssn: student.ssn,
            dob: student.dob,
            registered: false,
            status: '',
            message: '',
            checkTime: new Date().toLocaleString()
        };

        try {
            // 步骤1: 验证出生日期
            const dobData = { month, day, year };
            const dobResult = await apiCall('checkDob', dobData);
            
            if (dobResult.status !== 200) {
                result.status = 'DOB_ERROR';
                result.message = `出生日期验证失败: HTTP ${dobResult.status}`;
                return result;
            }

            // 步骤2: 尝试提交个人信息来检查是否已注册
            const personalData = {
                firstName: student.name,
                middleInitial: student.mi,
                lastName: student.last,
                birthMonth: month,
                birthDay: day,
                birthYear: year,
                ssn: student.ssn
            };

            const personalResult = await apiCall('a', personalData);
            
            if (personalResult.status === 409) {
                // HTTP 409 = Conflict，表示账户已存在
                result.registered = true;
                result.status = 'REGISTERED';
                result.message = '账户已注册';
            } else if (personalResult.status === 200) {
                // HTTP 200 = 成功，表示可以注册（账户不存在）
                result.registered = false;
                result.status = 'AVAILABLE';
                result.message = '可以注册';
            } else {
                // 其他状态码
                result.status = 'UNKNOWN';
                result.message = `未知状态: HTTP ${personalResult.status}`;
            }

        } catch (error) {
            result.status = 'ERROR';
            result.message = `检查异常: ${error.message}`;
        }

        return result;
    }

    // 批量检查所有学生
    async function checkAllStudents() {
        if (isChecking) return;
        
        isChecking = true;
        checkResults = [];
        
        const checkBtn = document.getElementById('check-btn');
        const downloadBtn = document.getElementById('download-btn');
        
        checkBtn.disabled = true;
        checkBtn.textContent = '检查中...';
        downloadBtn.disabled = true;

        updateStatus('开始批量检查注册状态...');

        try {
            for (let i = 0; i < STUDENTS.length; i++) {
                const student = STUDENTS[i];
                updateProgress(i + 1, STUDENTS.length);
                
                const result = await checkStudentRegistration(student);
                checkResults.push(result);
                
                // 显示结果
                if (result.registered) {
                    console.log(`✅ ${result.name} - 已注册`);
                    updateStatus(`✅ ${result.name} - 已注册`);
                } else if (result.status === 'AVAILABLE') {
                    console.log(`🆕 ${result.name} - 可注册`);
                    updateStatus(`🆕 ${result.name} - 可注册`);
                } else {
                    console.log(`❌ ${result.name} - ${result.message}`);
                    updateStatus(`❌ ${result.name} - ${result.message}`);
                }

                // 更新结果显示
                updateResultsDisplay();

                // 添加延迟（除了最后一个）
                if (i < STUDENTS.length - 1) {
                    const delay = 2000 + Math.random() * 2000; // 2-4秒随机延迟
                    updateStatus(`等待 ${Math.round(delay/1000)} 秒后检查下一个...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }

            updateStatus('🎉 所有学生检查完成！');
            
        } catch (error) {
            updateStatus(`❌ 检查过程出错: ${error.message}`);
        } finally {
            isChecking = false;
            checkBtn.disabled = false;
            checkBtn.textContent = '重新检查';
            downloadBtn.disabled = false;
        }
    }

    // 创建控制界面
    function createUI() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed; top: 20px; right: 20px; width: 320px;
            background: white; border: 2px solid #007bff; border-radius: 8px;
            padding: 15px; z-index: 10000; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif; font-size: 14px; max-height: 80vh; overflow-y: auto;
        `;

        panel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #007bff;">🔍 FSA 注册状态检查器</h4>
            <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                <strong>功能:</strong> 检查账户是否已被注册<br>
                <strong>学生数量:</strong> ${STUDENTS.length}<br>
                <strong>检查方式:</strong> API调用
            </div>
            <button id="check-btn" style="
                background: #007bff; color: white; border: none; 
                padding: 10px 20px; border-radius: 4px; cursor: pointer; width: 100%; margin-bottom: 10px;
            ">开始检查注册状态</button>
            <button id="download-btn" style="
                background: #28a745; color: white; border: none; 
                padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%;
            " disabled>下载检查结果</button>
            <div id="status" style="margin-top: 10px; font-size: 12px; color: #666;">
                准备就绪
            </div>
            <div id="progress" style="margin-top: 5px; font-size: 12px; color: #666;">
                0/${STUDENTS.length}
            </div>
            <div id="results" style="margin-top: 10px; font-size: 12px; border-top: 1px solid #eee; padding-top: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">检查结果:</div>
                <div id="results-content">等待开始检查...</div>
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('check-btn').addEventListener('click', checkAllStudents);
        document.getElementById('download-btn').addEventListener('click', downloadResults);
    }

    // 更新状态显示
    function updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) statusEl.textContent = message;
    }

    // 更新进度显示
    function updateProgress(current, total) {
        const progressEl = document.getElementById('progress');
        if (progressEl) progressEl.textContent = `${current}/${total}`;
    }

    // 更新结果显示
    function updateResultsDisplay() {
        const resultsEl = document.getElementById('results-content');
        if (!resultsEl || checkResults.length === 0) return;

        const registered = checkResults.filter(r => r.registered).length;
        const available = checkResults.filter(r => r.status === 'AVAILABLE').length;
        const errors = checkResults.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN' || r.status === 'DOB_ERROR').length;

        let html = `
            <div style="margin-bottom: 8px;">
                <span style="color: #28a745;">✅ 已注册: ${registered}</span> | 
                <span style="color: #007bff;">🆕 可注册: ${available}</span> | 
                <span style="color: #dc3545;">❌ 错误: ${errors}</span>
            </div>
        `;

        checkResults.forEach((result, index) => {
            let statusColor = '#666';
            let statusIcon = '❓';
            
            if (result.registered) {
                statusColor = '#28a745';
                statusIcon = '✅';
            } else if (result.status === 'AVAILABLE') {
                statusColor = '#007bff';
                statusIcon = '🆕';
            } else {
                statusColor = '#dc3545';
                statusIcon = '❌';
            }

            html += `
                <div style="margin-bottom: 5px; padding: 5px; background: #f8f9fa; border-radius: 3px;">
                    <div style="font-weight: bold; color: ${statusColor};">
                        ${statusIcon} ${result.name}
                    </div>
                    <div style="font-size: 11px; color: #666;">
                        SSN: ${result.ssn} | ${result.message}
                    </div>
                </div>
            `;
        });

        resultsEl.innerHTML = html;
    }

    // 下载检查结果
    function downloadResults() {
        if (checkResults.length === 0) {
            alert('没有检查结果可下载');
            return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
        
        // 生成报告
        let report = `=== FSA 注册状态检查报告 ===\n`;
        report += `检查时间: ${new Date().toLocaleString()}\n`;
        report += `总计学生: ${checkResults.length}\n`;
        
        const registered = checkResults.filter(r => r.registered).length;
        const available = checkResults.filter(r => r.status === 'AVAILABLE').length;
        const errors = checkResults.length - registered - available;
        
        report += `已注册: ${registered}\n`;
        report += `可注册: ${available}\n`;
        report += `检查失败: ${errors}\n\n`;
        
        report += `=== 详细结果 ===\n`;
        checkResults.forEach((result, index) => {
            report += `${index + 1}. ${result.name}\n`;
            report += `   SSN: ${result.ssn}\n`;
            report += `   出生日期: ${result.dob}\n`;
            report += `   状态: ${result.registered ? '已注册' : result.status === 'AVAILABLE' ? '可注册' : '检查失败'}\n`;
            report += `   详情: ${result.message}\n`;
            report += `   检查时间: ${result.checkTime}\n\n`;
        });

        // 下载文件
        const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        GM_download(url, `FSA_注册状态检查_${timestamp}.txt`, url);
        
        updateStatus('📁 检查结果已下载');
    }

    // 初始化
    setTimeout(() => {
        createUI();
        console.log('FSA 注册状态检查器已加载');
        console.log('待检查学生:', STUDENTS);
        updateStatus('FSA 注册状态检查器已就绪');
    }, 1000);

})();
