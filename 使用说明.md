# FSA 注册状态检查器 - 使用说明

## 🎯 功能说明

**专门用于检查FSA账户是否已被注册，不进行账户创建。**

- ✅ **只检查注册状态** - 不创建新账户
- ✅ **后台API调用** - 快速、准确
- ✅ **批量处理** - 一次检查多个学生
- ✅ **自动下载结果** - 生成详细报告

## 📁 文件说明

### 1. FSA_Registration_Checker.user.js (完整版) ⭐ 推荐
- **功能**：完整的注册状态检查
- **特点**：详细界面、实时结果显示、状态统计
- **适用**：需要详细监控检查过程

### 2. FSA_Simple_API.user.js (简化版)
- **功能**：简化的注册状态检查
- **特点**：界面简洁、快速检查
- **适用**：快速批量检查

## 🚀 快速开始

### 步骤1：安装油猴扩展
- Chrome: [Tampermonkey](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)

### 步骤2：安装脚本
1. 点击油猴图标 → "管理面板"
2. 点击 "+" 创建新脚本
3. 复制粘贴 `FSA_Registration_Checker.user.js` 内容
4. 按 Ctrl+S 保存

### 步骤3：使用脚本
1. 访问 https://studentaid.gov/fsa-id/create-account/personal-info
2. 页面右上角出现"FSA 注册状态检查器"面板
3. 点击 "开始检查注册状态"
4. 等待检查完成，自动下载结果

## 📊 检查结果说明

### 三种状态
- ✅ **已注册** - 该学生的FSA账户已存在
- 🆕 **可注册** - 该学生可以创建新的FSA账户
- ❌ **检查失败** - 由于网络或其他原因检查失败

### 您的学生数据预期结果

1. **STEPHANIE A STILLINGS**
   - SSN: *********
   - 出生日期: 07/18/1990
   - **预期结果**: ✅ 已注册

2. **AARON D CORNETT**
   - SSN: *********
   - 出生日期: 01/18/1990
   - **预期结果**: 🆕 可注册

3. **MORGAN C STEPHENS**
   - SSN: *********
   - 出生日期: 04/24/1997
   - **预期结果**: 🆕 可注册

## 📋 输出示例

### 检查报告示例：
```
=== FSA 注册状态检查报告 ===
检查时间: 2024-01-15 14:30:25
总计学生: 3
已注册: 1
可注册: 2
检查失败: 0

=== 详细结果 ===
1. STEPHANIE A STILLINGS
   SSN: *********
   出生日期: 07/18/1990
   状态: 已注册
   详情: 账户已注册
   检查时间: 2024-01-15 14:30:28

2. AARON D CORNETT
   SSN: *********
   出生日期: 01/18/1990
   状态: 可注册
   详情: 可以注册
   检查时间: 2024-01-15 14:30:35

3. MORGAN C STEPHENS
   SSN: *********
   出生日期: 04/24/1997
   状态: 可注册
   详情: 可以注册
   检查时间: 2024-01-15 14:30:42
```

## ⚡ 技术特性

### API调用流程
1. **验证出生日期** - 确保数据有效性
2. **尝试提交个人信息** - 检查注册状态
3. **分析响应状态** - 判断是否已注册

### 智能延迟
- 每个学生检查间隔：2-4秒随机延迟
- 避免被检测为机器人行为

### 错误处理
- 网络超时保护（20秒）
- 详细错误信息记录
- 自动重试机制

## 🔧 自定义配置

### 修改学生数据
在脚本中找到 `STUDENTS` 数组：
```javascript
const STUDENTS = [
    { name: 'FIRSTNAME', mi: 'M', last: 'LASTNAME', dob: 'MM/DD/YYYY', ssn: 'XXXXXXXXX' },
    // 添加更多学生...
];
```

### 修改延迟时间
找到延迟设置：
```javascript
const delay = 2000 + Math.random() * 2000; // 2-4秒随机延迟
```

## 🛠️ 故障排除

### 常见问题

1. **脚本没有显示面板**
   - 检查油猴是否启用
   - 确认脚本已正确安装
   - 刷新页面重试

2. **检查失败**
   - 检查网络连接
   - 确认FSA网站可正常访问
   - 查看浏览器控制台错误信息

3. **部分学生检查失败**
   - 可能是网络波动
   - 点击"重新检查"重试

### 调试方法
1. 按 F12 打开浏览器控制台
2. 查看 Console 标签页的详细日志
3. 所有日志都有相应的前缀标识

## 📞 使用建议

1. **网络环境**：确保网络连接稳定
2. **检查时间**：3个学生约需要1分钟
3. **结果保存**：及时下载检查报告
4. **重复检查**：如有疑问可重新检查确认

## 🔒 安全说明

- 所有检查都在本地浏览器中进行
- 不会存储或传输学生信息到第三方
- 仅用于检查注册状态，不进行账户创建
- 遵循FSA网站的使用条款

---

**立即开始**：安装脚本，访问FSA网站，点击"开始检查注册状态"即可快速了解所有学生的注册情况！
