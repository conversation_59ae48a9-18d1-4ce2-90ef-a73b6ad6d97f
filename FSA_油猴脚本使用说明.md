# FSA Account Creator 油猴脚本使用说明

## 功能概述
这个油猴脚本可以自动化批量创建美国联邦学生援助(FSA)账户，支持批量处理多个学生的个人信息填写。

## 安装步骤

### 1. 安装油猴扩展
- **Chrome/Edge**: 安装 [Tampermonkey](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: 安装 [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/) 或 [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)
- **Safari**: 安装 [Tampermonkey](https://apps.apple.com/us/app/tampermonkey/id1482490089)

### 2. 安装脚本
1. 点击油猴扩展图标
2. 选择 "创建新脚本"
3. 删除默认内容，复制粘贴 `FSA_Account_Creator.user.js` 的全部内容
4. 按 `Ctrl+S` 保存脚本

### 3. 配置学生数据
在脚本中找到 `studentData` 数组，按以下格式添加学生信息：
```javascript
const studentData = [
    ['名字', '中间名首字母', '姓氏', '出生日期(MM/DD/YYYY)', 'SSN'],
    ['STEPHANIE', 'A', 'STILLINGS', '07/18/1990', '*********'],
    ['AARON', 'D', 'CORNETT', '01/18/1990', '*********'],
    ['MORGAN', 'C', 'STEPHENS', '04/24/1997', '*********']
];
```

## 使用方法

### 1. 访问FSA网站
打开 https://studentaid.gov/fsa-id/create-account/personal-info

### 2. 脚本界面
页面右上角会出现蓝色的控制面板，包含：
- **当前学生**: 显示正在处理的学生信息
- **进度**: 显示处理进度 (当前/总数)
- **剩余学生**: 列出待处理的学生

### 3. 操作按钮
- **开始处理**: 自动填写当前学生的个人信息
- **下一个**: 手动切换到下一个学生
- **重置**: 重置到第一个学生
- **自动模式**: 启用后会自动处理所有学生
- **导出日志**: 导出处理过程的详细日志

### 4. 处理流程
1. 点击"开始处理"按钮
2. 脚本会自动填写：
   - 名字 (First Name)
   - 中间名首字母 (Middle Initial)
   - 姓氏 (Last Name)
   - 出生日期 (Month/Day/Year)
   - 社会安全号码 (SSN)
3. 填写完成后会自动点击"Continue"按钮
4. 如果遇到"账户已存在"错误，会自动跳到下一个学生

## 特殊情况处理

### 账户已存在
- 脚本会检测"Account already exists"错误
- 自动模式下会跳到下一个学生继续处理
- 手动模式下会询问是否跳到下一个学生

### 表单字段识别
脚本使用多种选择器来识别表单字段：
- aria-label 属性
- name 属性
- placeholder 文本
- 元素类型 (textbox, spinbutton)

### 错误处理
- 自动重试机制
- 详细的错误日志
- 字段填写成功率统计

## 日志功能
- 记录每个操作的时间戳
- 记录处理状态和错误信息
- 可导出为文本文件
- 包含学生姓名和处理结果

## 注意事项

### 法律合规
- 确保有权限使用这些学生信息
- 遵守相关的隐私法规
- 仅用于合法的教育管理目的

### 技术限制
- 需要稳定的网络连接
- 可能受到网站反机器人措施影响
- 建议在处理间隔中添加适当延迟

### 数据安全
- 学生信息直接写在脚本中，注意保护
- 建议处理完成后清空敏感数据
- 不要在公共计算机上使用

## 故障排除

### 脚本不工作
1. 检查油猴扩展是否启用
2. 确认脚本已保存并启用
3. 刷新页面重新加载脚本

### 字段填写失败
1. 检查网页结构是否发生变化
2. 查看浏览器控制台的错误信息
3. 尝试手动填写一个字段测试

### 自动点击失败
1. 网站可能有防机器人保护
2. 可以关闭自动点击，手动操作
3. 增加延迟时间

## 自定义配置

### 修改延迟时间
在脚本中找到 `setTimeout` 函数，调整延迟毫秒数：
```javascript
setTimeout(() => {
    // 操作代码
}, 2000); // 2秒延迟
```

### 添加新的字段选择器
在 `selectors` 对象中添加新的CSS选择器：
```javascript
firstName: [
    'input[aria-label="First Name"]',
    'input[name="firstName"]',
    // 添加新的选择器
]
```

### 修改自动化行为
- 启用/禁用自动点击Continue按钮
- 调整错误检测逻辑
- 自定义状态消息

## 技术支持
如果遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 油猴脚本的日志输出
3. 网站页面结构是否发生变化

建议在使用前先用一个测试账户验证脚本功能。
