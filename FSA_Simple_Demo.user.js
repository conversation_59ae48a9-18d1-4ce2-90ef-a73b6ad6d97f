// ==UserScript==
// @name         FSA Account Creator - 简化演示版
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  FSA账户创建的简化演示版本
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 测试数据 - 您的三个学生
    const students = [
        {
            firstName: 'STEPHANIE',
            middleInitial: 'A',
            lastName: 'STILLINGS',
            birthDate: '07/18/1990',
            ssn: '*********'
        },
        {
            firstName: 'AARON',
            middleInitial: 'D', 
            lastName: 'CORNETT',
            birthDate: '01/18/1990',
            ssn: '*********'
        },
        {
            firstName: 'MORGAN',
            middleInitial: 'C',
            lastName: 'STEPHENS',
            birthDate: '04/24/1997',
            ssn: '*********'
        }
    ];

    let currentIndex = 0;

    // 创建简单的控制按钮
    function createControls() {
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            z-index: 9999;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            min-width: 250px;
        `;

        container.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #007bff;">FSA 自动填写</h4>
            <div id="current-info" style="margin-bottom: 10px; font-size: 12px;"></div>
            <button id="fill-btn" style="background: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 5px;">填写当前</button>
            <button id="next-btn" style="background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">下一个</button>
            <div id="status" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
        `;

        document.body.appendChild(container);
        updateInfo();
        bindEvents();
    }

    // 更新当前学生信息显示
    function updateInfo() {
        const infoEl = document.getElementById('current-info');
        const statusEl = document.getElementById('status');
        
        if (currentIndex < students.length) {
            const student = students[currentIndex];
            infoEl.innerHTML = `
                <strong>学生 ${currentIndex + 1}/${students.length}</strong><br>
                ${student.firstName} ${student.lastName}<br>
                生日: ${student.birthDate}<br>
                SSN: ${student.ssn}
            `;
            statusEl.textContent = '准备填写';
        } else {
            infoEl.innerHTML = '<strong>所有学生已处理完成</strong>';
            statusEl.textContent = '完成';
        }
    }

    // 绑定按钮事件
    function bindEvents() {
        document.getElementById('fill-btn').addEventListener('click', fillCurrentStudent);
        document.getElementById('next-btn').addEventListener('click', nextStudent);
    }

    // 填写当前学生信息
    function fillCurrentStudent() {
        if (currentIndex >= students.length) {
            alert('所有学生已处理完成！');
            return;
        }

        const student = students[currentIndex];
        const statusEl = document.getElementById('status');
        
        statusEl.textContent = '正在填写...';
        statusEl.style.color = '#007bff';

        try {
            // 检查是否有错误提示
            const errorAlert = document.querySelector('[role="alert"]');
            if (errorAlert && errorAlert.textContent.includes('Account already exists')) {
                statusEl.textContent = '账户已存在！';
                statusEl.style.color = '#dc3545';
                if (confirm('账户已存在，是否跳到下一个学生？')) {
                    nextStudent();
                }
                return;
            }

            // 填写表单字段
            fillField('First Name', student.firstName);
            fillField('Middle Initial', student.middleInitial);
            fillField('Last Name', student.lastName);
            
            // 填写出生日期
            const [month, day, year] = student.birthDate.split('/');
            fillField('Month', month);
            fillField('Day', day);
            fillField('Year', year);
            
            // 填写SSN
            fillField('Social Security Number', student.ssn);

            statusEl.textContent = '填写完成！请检查并点击Continue';
            statusEl.style.color = '#28a745';

        } catch (error) {
            statusEl.textContent = '填写失败: ' + error.message;
            statusEl.style.color = '#dc3545';
            console.error('填写错误:', error);
        }
    }

    // 通用字段填写函数
    function fillField(fieldName, value) {
        // 多种选择器策略
        const selectors = [
            `input[aria-label="${fieldName}"]`,
            `input[aria-label*="${fieldName}"]`,
            `input[name*="${fieldName.toLowerCase().replace(/\s+/g, '')}"]`,
            `input[placeholder*="${fieldName}"]`,
            `textbox[name="${fieldName}"]`,
            `spinbutton[name="${fieldName}"]`
        ];

        let filled = false;
        for (let selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                element.focus();
                element.value = value;
                
                // 触发事件
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new Event('blur', { bubbles: true }));
                
                console.log(`✓ 填写 ${fieldName}: ${value}`);
                filled = true;
                break;
            }
        }

        if (!filled) {
            console.warn(`✗ 未找到字段: ${fieldName}`);
        }
    }

    // 切换到下一个学生
    function nextStudent() {
        if (currentIndex < students.length - 1) {
            currentIndex++;
            updateInfo();
            
            // 清空当前表单
            const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
            inputs.forEach(input => {
                input.value = '';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            });
            
            document.getElementById('status').textContent = '已切换到下一个学生';
            document.getElementById('status').style.color = '#007bff';
        } else {
            alert('已经是最后一个学生了！');
        }
    }

    // 页面加载完成后初始化
    function init() {
        // 等待页面完全加载
        setTimeout(() => {
            createControls();
            console.log('FSA 自动填写脚本已加载');
            console.log('学生数据:', students);
        }, 1000);
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
