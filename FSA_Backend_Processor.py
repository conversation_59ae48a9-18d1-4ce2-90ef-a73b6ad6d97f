#!/usr/bin/env python3
"""
FSA Account Creator - 后台协议处理器
基于HTTP请求的批量账户创建工具
"""

import requests
import json
import time
import random
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fsa_processor.log'),
        logging.StreamHandler()
    ]
)

class FSAAccountProcessor:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://studentaid.gov"
        self.results = []
        
        # 设置请求头，模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://studentaid.gov/fsa-id/create-account/personal-info',
            'Origin': 'https://studentaid.gov'
        })

    def initialize_session(self) -> bool:
        """初始化会话，获取必要的cookies和tokens"""
        try:
            logging.info("正在初始化会话...")
            
            # 1. 访问主页面获取初始cookies
            response = self.session.get(f"{self.base_url}/fsa-id/create-account/personal-info")
            if response.status_code != 200:
                logging.error(f"无法访问主页面: {response.status_code}")
                return False
            
            # 2. 获取会话信息
            session_response = self.session.get(f"{self.base_url}/app/api/gateway/session")
            if session_response.status_code == 200:
                logging.info("会话初始化成功")
                return True
            else:
                logging.error(f"会话初始化失败: {session_response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"初始化会话时出错: {str(e)}")
            return False

    def check_account_exists(self, student_data: Dict) -> Tuple[bool, str]:
        """检查账户是否已存在"""
        try:
            # 构造检查出生日期的请求
            dob_data = {
                "month": student_data["birth_month"],
                "day": student_data["birth_day"], 
                "year": student_data["birth_year"]
            }
            
            response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/checkDob",
                json=dob_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logging.info(f"出生日期检查完成: {student_data['first_name']} {student_data['last_name']}")
                return False, "出生日期验证通过"
            else:
                logging.warning(f"出生日期检查失败: {response.status_code}")
                return True, f"出生日期检查失败: {response.status_code}"
                
        except Exception as e:
            logging.error(f"检查账户时出错: {str(e)}")
            return True, f"检查出错: {str(e)}"

    def submit_personal_info(self, student_data: Dict) -> Tuple[bool, str]:
        """提交个人信息"""
        try:
            # 构造个人信息数据
            personal_data = {
                "firstName": student_data["first_name"],
                "middleInitial": student_data["middle_initial"],
                "lastName": student_data["last_name"],
                "birthMonth": student_data["birth_month"],
                "birthDay": student_data["birth_day"],
                "birthYear": student_data["birth_year"],
                "ssn": student_data["ssn"]
            }
            
            response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/a",
                json=personal_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logging.info(f"个人信息提交成功: {student_data['first_name']} {student_data['last_name']}")
                return True, "个人信息提交成功"
            elif response.status_code == 409:
                # 账户已存在
                logging.warning(f"账户已存在: {student_data['first_name']} {student_data['last_name']} (SSN: {student_data['ssn']})")
                return False, "账户已存在"
            else:
                error_msg = f"提交失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    if 'message' in error_detail:
                        error_msg += f" - {error_detail['message']}"
                except:
                    pass
                logging.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"提交个人信息时出错: {str(e)}"
            logging.error(error_msg)
            return False, error_msg

    def process_single_student(self, student_data: Dict) -> Dict:
        """处理单个学生的账户创建"""
        start_time = datetime.now()
        result = {
            "student_name": f"{student_data['first_name']} {student_data['middle_initial']} {student_data['last_name']}",
            "ssn": student_data["ssn"],
            "start_time": start_time.isoformat(),
            "success": False,
            "message": "",
            "next_step": ""
        }
        
        try:
            logging.info(f"开始处理学生: {result['student_name']}")
            
            # 1. 检查账户是否存在
            exists, check_msg = self.check_account_exists(student_data)
            if exists:
                result["message"] = check_msg
                result["next_step"] = "账户检查失败，跳过"
                return result
            
            # 2. 提交个人信息
            success, submit_msg = self.submit_personal_info(student_data)
            result["success"] = success
            result["message"] = submit_msg
            
            if success:
                result["next_step"] = "个人信息已提交，需要继续填写账户信息"
                logging.info(f"✅ 成功处理: {result['student_name']}")
            else:
                result["next_step"] = "处理失败，请检查错误信息"
                logging.error(f"❌ 处理失败: {result['student_name']} - {submit_msg}")
            
        except Exception as e:
            result["message"] = f"处理过程中出现异常: {str(e)}"
            result["next_step"] = "异常终止"
            logging.error(f"处理学生时出现异常: {result['student_name']} - {str(e)}")
        
        finally:
            end_time = datetime.now()
            result["end_time"] = end_time.isoformat()
            result["duration"] = str(end_time - start_time)
        
        return result

    def process_batch(self, students_data: List[Dict], delay_range: Tuple[int, int] = (2, 5)) -> List[Dict]:
        """批量处理学生数据"""
        logging.info(f"开始批量处理 {len(students_data)} 个学生账户")
        
        # 初始化会话
        if not self.initialize_session():
            logging.error("会话初始化失败，无法继续处理")
            return []
        
        results = []
        
        for i, student_data in enumerate(students_data, 1):
            logging.info(f"处理进度: {i}/{len(students_data)}")
            
            # 处理单个学生
            result = self.process_single_student(student_data)
            results.append(result)
            
            # 添加随机延迟，避免被检测为机器人
            if i < len(students_data):
                delay = random.randint(delay_range[0], delay_range[1])
                logging.info(f"等待 {delay} 秒后处理下一个学生...")
                time.sleep(delay)
        
        self.results = results
        return results

    def generate_report(self) -> str:
        """生成处理报告"""
        if not self.results:
            return "没有处理结果可报告"
        
        total = len(self.results)
        successful = sum(1 for r in self.results if r["success"])
        failed = total - successful
        
        report = f"""
=== FSA 账户批量创建处理报告 ===
处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总计学生: {total}
成功创建: {successful}
失败/跳过: {failed}
成功率: {(successful/total*100):.1f}%

详细结果:
"""
        
        for i, result in enumerate(self.results, 1):
            status = "✅ 成功" if result["success"] else "❌ 失败"
            report += f"""
{i}. {result['student_name']} (SSN: {result['ssn']})
   状态: {status}
   信息: {result['message']}
   下一步: {result['next_step']}
   耗时: {result['duration']}
"""
        
        return report

def main():
    """主函数 - 处理您的三个学生数据"""
    
    # 您的学生数据
    students_data = [
        {
            "first_name": "STEPHANIE",
            "middle_initial": "A",
            "last_name": "STILLINGS",
            "birth_month": "07",
            "birth_day": "18", 
            "birth_year": "1990",
            "ssn": "491040179"
        },
        {
            "first_name": "AARON",
            "middle_initial": "D",
            "last_name": "CORNETT",
            "birth_month": "01",
            "birth_day": "18",
            "birth_year": "1990", 
            "ssn": "*********"
        },
        {
            "first_name": "MORGAN",
            "middle_initial": "C",
            "last_name": "STEPHENS",
            "birth_month": "04",
            "birth_day": "24",
            "birth_year": "1997",
            "ssn": "*********"
        }
    ]
    
    # 创建处理器实例
    processor = FSAAccountProcessor()
    
    # 批量处理
    results = processor.process_batch(students_data)
    
    # 生成并显示报告
    report = processor.generate_report()
    print(report)
    
    # 保存报告到文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"FSA_处理报告_{timestamp}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logging.info(f"处理完成！报告已保存到: {report_file}")
    
    return results

if __name__ == "__main__":
    main()
