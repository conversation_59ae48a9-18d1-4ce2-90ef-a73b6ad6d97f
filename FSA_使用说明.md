# FSA Account Creator 油猴脚本使用说明

## 功能概述
这个油猴脚本可以自动化批量创建美国联邦学生援助(FSA)账户，支持批量处理多个学生的个人信息。

## 安装步骤

### 1. 安装油猴扩展
- **Chrome/Edge**: 安装 [Tampermonkey](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: 安装 [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/) 或 [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)

### 2. 安装脚本
1. 点击油猴扩展图标
2. 选择"添加新脚本"
3. 删除默认内容，复制粘贴 `FSA_Account_Creator.user.js` 的全部内容
4. 按 `Ctrl+S` 保存脚本

## 使用方法

### 1. 准备学生数据
在脚本中修改 `studentData` 数组，格式如下：
```javascript
const studentData = [
    ['名字', '中间名首字母', '姓氏', '出生日期(MM/DD/YYYY)', 'SSN'],
    ['STEPHANIE', 'A', 'STILLINGS', '07/18/1990', '*********'],
    ['AARON', 'D', 'CORNETT', '01/18/1990', '*********'],
    ['MORGAN', 'C', 'STEPHENS', '04/24/1997', '*********']
];
```

### 2. 访问FSA网站
打开 https://studentaid.gov/fsa-id/create-account/personal-info

### 3. 使用控制面板
脚本会在页面右上角显示一个控制面板，包含以下功能：

#### 按钮功能
- **开始处理**: 填写当前学生的个人信息
- **下一个**: 切换到下一个学生
- **重置**: 回到第一个学生
- **导出日志**: 下载处理日志文件

#### 模式选择
- **手动模式**: 需要手动点击按钮处理每个学生
- **自动模式**: 勾选"自动模式"后，脚本会自动处理所有学生

### 4. 处理流程

#### 手动模式流程
1. 点击"开始处理"填写当前学生信息
2. 检查填写结果，手动点击页面上的"Continue"按钮
3. 如果遇到"账户已存在"错误，点击"下一个"跳过
4. 重复步骤1-3直到处理完所有学生

#### 自动模式流程
1. 勾选"自动模式"
2. 点击"开始处理"
3. 脚本会自动：
   - 填写表单
   - 点击Continue按钮
   - 处理"账户已存在"错误
   - 自动跳到下一个学生
   - 继续处理直到完成

## 功能特性

### 智能表单填写
- 支持多种选择器策略，适应不同的页面结构
- 自动触发所有必要的事件（input, change, blur, keyup）
- 实时验证填写结果

### 错误处理
- 自动检测"账户已存在"错误
- 提供详细的错误信息和处理建议
- 支持跳过已存在的账户

### 日志记录
- 记录所有操作的时间戳
- 包含处理状态和错误信息
- 支持导出日志文件

### 进度跟踪
- 显示当前处理的学生信息
- 显示总体进度（已处理/总数）
- 显示剩余待处理的学生列表

## 注意事项

### 数据安全
- 所有学生数据仅在本地浏览器中处理
- 不会向第三方服务器发送任何信息
- 建议处理完成后清除浏览器数据

### 使用限制
- 仅适用于 studentaid.gov 网站
- 需要有效的学生信息（姓名、出生日期、SSN）
- 每个SSN只能创建一个账户

### 法律合规
- 确保有权限处理这些学生信息
- 遵守相关的隐私法规
- 仅用于合法的教育管理目的

## 故障排除

### 常见问题

#### 1. 脚本没有加载
- 检查油猴扩展是否已启用
- 确认脚本已正确安装并启用
- 刷新页面重试

#### 2. 表单填写失败
- 检查页面结构是否发生变化
- 查看浏览器控制台的错误信息
- 尝试手动填写一个字段测试

#### 3. Continue按钮无法点击
- 检查是否所有必填字段都已填写
- 确认没有验证错误
- 手动点击Continue按钮

#### 4. 自动模式不工作
- 确认已勾选"自动模式"选项
- 检查是否有弹窗阻止自动操作
- 查看控制面板的状态信息

### 调试方法
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的日志信息
3. 所有操作都会有 `[FSA Auto]` 前缀的日志
4. 导出日志文件进行详细分析

## 更新和维护

### 脚本更新
如果FSA网站结构发生变化，可能需要更新脚本中的选择器：
- 修改 `selectors` 对象中的CSS选择器
- 更新错误检测逻辑
- 调整自动点击逻辑

### 数据更新
- 定期更新 `studentData` 数组
- 验证学生信息的准确性
- 备份处理日志

## 技术支持
如遇到技术问题，请：
1. 导出详细的处理日志
2. 截图控制面板状态
3. 记录具体的错误信息
4. 提供浏览器和油猴版本信息
