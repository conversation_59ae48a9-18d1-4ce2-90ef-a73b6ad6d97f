#!/usr/bin/env python3
"""
FSA 账户批量创建 - 快速启动脚本
一键运行，自动处理您的学生数据
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🎓 FSA 账户批量创建工具")
    print("📅 处理时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    try:
        import requests
        print("✅ requests 库已安装")
        return True
    except ImportError:
        print("❌ 缺少 requests 库")
        print("正在安装 requests...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
            print("✅ requests 库安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ requests 库安装失败，请手动安装：pip install requests")
            return False

def show_menu():
    """显示菜单"""
    print("\n📋 请选择处理模式：")
    print("1. 基础模式 - 只验证个人信息")
    print("2. 完整模式 - 创建完整账户（推荐）")
    print("3. 查看学生数据")
    print("4. 退出")
    print("-" * 40)

def show_student_data():
    """显示学生数据"""
    print("\n👥 待处理的学生数据：")
    students = [
        "1. STEPHANIE ANNE STILLINGS (07/18/1990, SSN: 491040179)",
        "2. AARON DOUGLAS CORNETT (01/18/1990, SSN: 371116023)", 
        "3. MORGAN CATHERINE STEPHENS (04/24/1997, SSN: 002908966)"
    ]
    
    for student in students:
        print(f"   {student}")
    
    print("\n📝 预期结果：")
    print("   - STEPHANIE: 账户可能已存在，将跳过")
    print("   - AARON: 预计可以成功创建")
    print("   - MORGAN: 预计可以成功创建")

def run_basic_mode():
    """运行基础模式"""
    print("\n🚀 启动基础模式...")
    print("📋 功能：验证个人信息，检查账户状态")
    
    if not os.path.exists("FSA_Backend_Processor.py"):
        print("❌ 找不到 FSA_Backend_Processor.py 文件")
        return False
    
    try:
        print("⏳ 正在处理，请稍候...")
        result = subprocess.run([sys.executable, "FSA_Backend_Processor.py"], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 基础模式处理完成")
            print("\n📊 处理结果：")
            print(result.stdout)
            return True
        else:
            print("❌ 处理过程中出现错误：")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 处理超时，请检查网络连接")
        return False
    except Exception as e:
        print(f"❌ 运行异常：{str(e)}")
        return False

def run_complete_mode():
    """运行完整模式"""
    print("\n🚀 启动完整模式...")
    print("📋 功能：创建完整账户（个人信息 + 账户信息）")
    
    if not os.path.exists("FSA_Complete_Processor.py"):
        print("❌ 找不到 FSA_Complete_Processor.py 文件")
        return False
    
    try:
        print("⏳ 正在处理，预计需要1-2分钟...")
        print("📝 实时日志将显示在下方：")
        print("-" * 50)
        
        # 实时显示输出
        process = subprocess.Popen([sys.executable, "FSA_Complete_Processor.py"],
                                 stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, bufsize=1)
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        if process.returncode == 0:
            print("-" * 50)
            print("✅ 完整模式处理完成")
            
            # 显示生成的文件
            print("\n📁 生成的文件：")
            for file in os.listdir('.'):
                if file.startswith('FSA_') and (file.endswith('.txt') or file.endswith('.json')):
                    print(f"   📄 {file}")
            
            return True
        else:
            print("❌ 处理过程中出现错误")
            return False
            
    except Exception as e:
        print(f"❌ 运行异常：{str(e)}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，无法继续")
        input("按回车键退出...")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == "1":
                success = run_basic_mode()
                if success:
                    print("\n🎉 基础模式处理成功！")
                else:
                    print("\n😞 基础模式处理失败")
                
            elif choice == "2":
                print("\n⚠️  注意：完整模式将尝试创建真实的FSA账户")
                confirm = input("确认继续？(y/N): ").strip().lower()
                
                if confirm in ['y', 'yes']:
                    success = run_complete_mode()
                    if success:
                        print("\n🎉 完整模式处理成功！")
                        print("📋 请查看生成的报告文件获取详细结果")
                    else:
                        print("\n😞 完整模式处理失败")
                else:
                    print("❌ 已取消操作")
                
            elif choice == "3":
                show_student_data()
                
            elif choice == "4":
                print("\n👋 感谢使用 FSA 账户批量创建工具！")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
            
            if choice in ["1", "2"]:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"\n❌ 程序异常：{str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
