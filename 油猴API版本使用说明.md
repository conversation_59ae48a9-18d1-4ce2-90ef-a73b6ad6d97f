# FSA 油猴API版本使用说明

## 🎯 核心优势

### 相比表单填写版本的优势：
- ✅ **纯后台处理** - 直接调用API，无需模拟浏览器操作
- ✅ **速度更快** - 跳过页面渲染，直接数据交互
- ✅ **更稳定** - 不受页面变化影响
- ✅ **自动下载结果** - 处理完成自动下载报告
- ✅ **完整账户信息** - 自动生成用户名、邮箱、密码

## 📁 文件说明

### 1. FSA_API_Processor.user.js (完整版)
- **功能**：完整的API处理流程
- **特点**：详细的状态显示、步骤跟踪、结果摘要
- **适用**：需要详细监控处理过程

### 2. FSA_Simple_API.user.js (简化版) ⭐ 推荐
- **功能**：简化的API处理流程
- **特点**：界面简洁、操作简单、快速处理
- **适用**：快速批量处理，推荐使用

## 🚀 快速开始

### 步骤1：安装油猴扩展
- Chrome: [Tampermonkey](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- Firefox: [Tampermonkey](https://addons.mozilla.org/firefox/addon/tampermonkey/)

### 步骤2：安装脚本
1. 点击油猴图标 → "管理面板"
2. 点击 "+" 创建新脚本
3. 复制粘贴 `FSA_Simple_API.user.js` 内容
4. 按 Ctrl+S 保存

### 步骤3：使用脚本
1. 访问 https://studentaid.gov/fsa-id/create-account/personal-info
2. 页面右上角出现控制面板
3. 点击 "开始批量处理"
4. 等待处理完成，自动下载结果

## 🔧 技术原理

### API调用流程
```
1. checkDob API - 验证出生日期
   ↓
2. registration/a API - 提交个人信息  
   ↓
3. registration/b API - 创建账户信息
   ↓
4. 返回完整账户信息
```

### 关键API端点
- `POST /app/api/auth/registration/checkDob` - 出生日期验证
- `POST /app/api/auth/registration/a` - 个人信息提交
- `POST /app/api/auth/registration/b` - 账户信息创建

### 自动生成规则
- **用户名**: `firstname.lastname + SSN后4位`
- **邮箱**: `用户名@gmail.com`
- **密码**: 8位随机密码（包含大小写字母和数字）

## 📊 预期处理结果

### 您的三个学生数据：

1. **STEPHANIE A STILLINGS**
   - SSN: *********
   - 出生日期: 07/18/1990
   - **预期**: 账户已存在，跳过

2. **AARON D CORNETT**
   - SSN: *********  
   - 出生日期: 01/18/1990
   - **预期**: 成功创建
   - **生成账户**: <EMAIL>

3. **MORGAN C STEPHENS**
   - SSN: 002908966
   - 出生日期: 04/24/1997
   - **预期**: 成功创建
   - **生成账户**: <EMAIL>

## 📋 输出示例

### 成功创建的账户信息：
```
2. AARON D CORNETT
   状态: ✅ 成功
   信息: 账户创建成功
   用户名: aaron.cornett6023
   邮箱: <EMAIL>
   密码: Kp9mX2nL
```

### 账户已存在的情况：
```
1. STEPHANIE A STILLINGS
   状态: ❌ 失败
   信息: 账户已存在
```

## ⚡ 处理特性

### 智能延迟
- 每个学生处理间隔：3-5秒随机延迟
- API调用间隔：1-2秒延迟
- 避免被检测为机器人

### 错误处理
- 自动重试机制
- 详细错误信息记录
- 超时保护（30秒）

### 结果管理
- 实时状态更新
- 自动下载处理报告
- JSON格式数据导出

## 🛠️ 自定义配置

### 修改学生数据
在脚本中找到 `STUDENTS` 数组：
```javascript
const STUDENTS = [
    { name: 'FIRSTNAME', mi: 'M', last: 'LASTNAME', dob: 'MM/DD/YYYY', ssn: 'XXXXXXXXX' },
    // 添加更多学生...
];
```

### 修改邮箱域名
在 `generatePassword` 函数附近找到：
```javascript
const email = `${username}@gmail.com`;
```
可以改为其他域名如 `@yahoo.com`, `@hotmail.com` 等

## 🔍 监控和调试

### 浏览器控制台
- 按 F12 打开开发者工具
- 查看 Console 标签页
- 所有处理日志都有 `[FSA API]` 前缀

### 状态监控
- 控制面板实时显示当前状态
- 进度条显示处理进度
- 成功/失败状态即时反馈

## 🚨 注意事项

### 网络要求
- 确保网络连接稳定
- 建议在网络较好的环境下运行

### 处理时间
- 3个学生约需要1-2分钟
- 包含必要的延迟时间

### 结果保存
- 处理报告自动下载到默认下载文件夹
- 文件名包含时间戳便于区分

## 🔧 故障排除

### 常见问题

1. **脚本没有显示控制面板**
   - 检查油猴是否启用
   - 确认脚本已正确安装
   - 刷新页面重试

2. **API调用失败**
   - 检查网络连接
   - 确认FSA网站可正常访问
   - 查看控制台错误信息

3. **处理中断**
   - 网络问题导致，可重新开始
   - 已处理的结果会保留

### 调试方法
1. 打开浏览器控制台（F12）
2. 查看详细的API调用日志
3. 检查网络请求状态

## 🎉 使用建议

1. **首次使用**：建议先用简化版本测试
2. **批量处理**：确保网络稳定后再开始
3. **结果备份**：及时保存下载的报告文件
4. **定期更新**：关注脚本更新以适应网站变化

---

**立即开始**：安装 `FSA_Simple_API.user.js` 脚本，访问FSA网站，点击"开始批量处理"即可！
