// ==UserScript==
// @name         FSA Account Creator - 批量创建联邦学生援助账户
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动化批量创建FSA账户的油猴脚本
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 学生数据 - 格式: [firstName, middleInitial, lastName, birthDate(MM/DD/YYYY), SSN]
    const studentData = [
        ['STEPHANIE', 'A', 'STILLINGS', '07/18/1990', '*********'],
        ['AARON', 'D', 'CORNETT', '01/18/1990', '*********'],
        ['MORGAN', 'C', 'STEPHENS', '04/24/1997', '*********']
    ];

    let currentStudentIndex = 0;
    let isProcessing = false;
    let processLog = [];
    let autoMode = false;

    // 创建控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'fsa-auto-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: #fff;
            border: 2px solid #0066cc;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;

        panel.innerHTML = `
            <div style="background: #0066cc; color: white; padding: 8px; margin: -15px -15px 10px -15px; border-radius: 6px 6px 0 0;">
                <strong>FSA 账户批量创建器</strong>
            </div>
            <div id="status">准备就绪</div>
            <div style="margin: 10px 0;">
                当前学生: <span id="current-student">未选择</span>
            </div>
            <div style="margin: 10px 0;">
                进度: <span id="progress">0/3</span>
            </div>
            <button id="start-btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">开始处理</button>
            <button id="next-btn" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 5px;">下一个</button>
            <button id="reset-btn" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">重置</button>
            <div style="margin-top: 10px;">
                <label style="font-size: 12px;">
                    <input type="checkbox" id="auto-mode" style="margin-right: 5px;">
                    自动模式 (自动处理所有学生)
                </label>
            </div>
            <button id="export-log-btn" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-top: 5px; font-size: 12px;">导出日志</button>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <div>剩余学生:</div>
                <div id="remaining-students"></div>
            </div>
        `;

        document.body.appendChild(panel);
        updateDisplay();
        bindEvents();
    }

    // 更新显示信息
    function updateDisplay() {
        const statusEl = document.getElementById('status');
        const currentStudentEl = document.getElementById('current-student');
        const progressEl = document.getElementById('progress');
        const remainingEl = document.getElementById('remaining-students');

        if (currentStudentIndex < studentData.length) {
            const student = studentData[currentStudentIndex];
            currentStudentEl.textContent = `${student[0]} ${student[1]} ${student[2]}`;
        } else {
            currentStudentEl.textContent = '全部完成';
        }

        progressEl.textContent = `${currentStudentIndex}/${studentData.length}`;

        // 显示剩余学生
        let remainingText = '';
        for (let i = currentStudentIndex; i < studentData.length; i++) {
            const student = studentData[i];
            remainingText += `${i + 1}. ${student[0]} ${student[2]}<br>`;
        }
        remainingEl.innerHTML = remainingText || '无';
    }

    // 绑定事件
    function bindEvents() {
        document.getElementById('start-btn').addEventListener('click', startProcessing);
        document.getElementById('next-btn').addEventListener('click', nextStudent);
        document.getElementById('reset-btn').addEventListener('click', resetProcess);
        document.getElementById('auto-mode').addEventListener('change', (e) => {
            autoMode = e.target.checked;
            updateStatus(autoMode ? '已启用自动模式' : '已禁用自动模式', 'info');
        });
        document.getElementById('export-log-btn').addEventListener('click', exportLog);
    }

    // 开始处理当前学生
    function startProcessing() {
        if (isProcessing) return;
        if (currentStudentIndex >= studentData.length) {
            updateStatus('所有学生已处理完成！', 'success');
            return;
        }

        isProcessing = true;
        updateStatus('正在填写表单...', 'processing');
        
        const student = studentData[currentStudentIndex];
        fillPersonalInfo(student);
    }

    // 填写个人信息
    function fillPersonalInfo(student) {
        const [firstName, middleInitial, lastName, birthDate, ssn] = student;
        const [month, day, year] = birthDate.split('/');

        try {
            // 更精确的选择器，基于实际页面结构
            const selectors = {
                firstName: [
                    'input[aria-label="First Name"]',
                    'textbox[name="First Name"]',
                    'input[name*="firstName"]',
                    'input[placeholder*="First"]'
                ],
                middleInitial: [
                    'input[aria-label="Middle Initial"]',
                    'textbox[name="Middle Initial"]',
                    'input[name*="middleInitial"]',
                    'input[placeholder*="Middle"]'
                ],
                lastName: [
                    'input[aria-label="Last Name"]',
                    'textbox[name="Last Name"]',
                    'input[name*="lastName"]',
                    'input[placeholder*="Last"]'
                ],
                month: [
                    'input[aria-label="Month"]',
                    'spinbutton[name="Month"]',
                    'input[name*="month"]',
                    'select[name*="month"]'
                ],
                day: [
                    'input[aria-label="Day"]',
                    'spinbutton[name="Day"]',
                    'input[name*="day"]',
                    'select[name*="day"]'
                ],
                year: [
                    'input[aria-label="Year"]',
                    'spinbutton[name="Year"]',
                    'input[name*="year"]',
                    'select[name*="year"]'
                ],
                ssn: [
                    'input[aria-label*="Social Security"]',
                    'textbox[name="Social Security Number"]',
                    'input[name*="ssn"]',
                    'input[name*="socialSecurity"]',
                    'input[placeholder*="Social Security"]'
                ]
            };

            // 智能填写函数
            function smartFill(selectorArray, value, fieldName) {
                for (let selector of selectorArray) {
                    const element = document.querySelector(selector);
                    if (element) {
                        // 清空现有值
                        element.value = '';
                        element.focus();

                        // 设置新值
                        element.value = value;

                        // 触发所有可能的事件
                        ['input', 'change', 'blur', 'keyup'].forEach(eventType => {
                            element.dispatchEvent(new Event(eventType, { bubbles: true }));
                        });

                        console.log(`✓ 已填写 ${fieldName}: ${value}`);
                        return true;
                    }
                }
                console.warn(`✗ 未找到 ${fieldName} 字段`);
                return false;
            }

            // 检查是否存在"账户已存在"错误
            const errorAlert = document.querySelector('div[role="alert"], .alert, .error-message');
            if (errorAlert && errorAlert.textContent.includes('Account already exists')) {
                updateStatus(`账户已存在 - ${firstName} ${lastName} (SSN: ${ssn})`, 'warning');

                if (autoMode) {
                    setTimeout(() => {
                        nextStudent();
                        setTimeout(() => {
                            if (currentStudentIndex < studentData.length) {
                                startProcessing();
                            }
                        }, 1000);
                    }, 2000);
                } else {
                    setTimeout(() => {
                        if (confirm('账户已存在，是否跳到下一个学生？')) {
                            nextStudent();
                        }
                    }, 2000);
                }
                return;
            }

            // 填写所有字段
            let successCount = 0;
            successCount += smartFill(selectors.firstName, firstName, '名字') ? 1 : 0;
            successCount += smartFill(selectors.middleInitial, middleInitial, '中间名') ? 1 : 0;
            successCount += smartFill(selectors.lastName, lastName, '姓氏') ? 1 : 0;
            successCount += smartFill(selectors.month, month, '月份') ? 1 : 0;
            successCount += smartFill(selectors.day, day, '日期') ? 1 : 0;
            successCount += smartFill(selectors.year, year, '年份') ? 1 : 0;
            successCount += smartFill(selectors.ssn, ssn, 'SSN') ? 1 : 0;

            if (successCount >= 6) {
                updateStatus(`表单填写完成 (${successCount}/7 字段) - ${firstName} ${lastName}`, 'success');

                // 延迟检查并自动点击Continue
                setTimeout(() => {
                    const continueBtn = document.querySelector('button:contains("Continue"), button[type="submit"], input[type="submit"][value*="Continue"]');
                    if (continueBtn) {
                        updateStatus('准备点击Continue按钮...', 'processing');
                        setTimeout(() => {
                            continueBtn.click();
                            updateStatus('已点击Continue，等待页面响应...', 'info');
                        }, 1000);
                    } else {
                        updateStatus('未找到Continue按钮，请手动点击', 'warning');
                    }
                }, 2000);
            } else {
                updateStatus(`表单填写不完整 (${successCount}/7 字段)`, 'warning');
            }

        } catch (error) {
            updateStatus('填写表单时出错: ' + error.message, 'error');
            console.error('填写表单错误:', error);
        }

        isProcessing = false;
    }

    // 下一个学生
    function nextStudent() {
        if (currentStudentIndex < studentData.length - 1) {
            currentStudentIndex++;
            updateDisplay();
            updateStatus('已切换到下一个学生', 'info');
            
            // 如果在个人信息页面，清空表单
            if (window.location.href.includes('personal-info')) {
                clearForm();
            }
        } else {
            updateStatus('已经是最后一个学生了', 'warning');
        }
    }

    // 重置处理
    function resetProcess() {
        currentStudentIndex = 0;
        isProcessing = false;
        updateDisplay();
        updateStatus('已重置到第一个学生', 'info');
        clearForm();
    }

    // 清空表单
    function clearForm() {
        const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
        inputs.forEach(input => {
            input.value = '';
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
        });
    }

    // 更新状态显示
    function updateStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        if (!statusEl) return;

        const colors = {
            'info': '#17a2b8',
            'success': '#28a745',
            'warning': '#ffc107',
            'error': '#dc3545',
            'processing': '#6f42c1'
        };

        statusEl.style.color = colors[type] || colors.info;
        statusEl.textContent = message;

        // 添加到日志
        const timestamp = new Date().toLocaleString();
        processLog.push({
            timestamp,
            message,
            type,
            studentIndex: currentStudentIndex,
            studentName: currentStudentIndex < studentData.length ?
                `${studentData[currentStudentIndex][0]} ${studentData[currentStudentIndex][2]}` : 'N/A'
        });

        console.log(`[FSA Auto] ${message}`);
    }

    // 导出日志
    function exportLog() {
        const logText = processLog.map(entry =>
            `[${entry.timestamp}] ${entry.type.toUpperCase()}: ${entry.message} (学生: ${entry.studentName})`
        ).join('\n');

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `FSA_处理日志_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);

        updateStatus('日志已导出', 'success');
    }

    // 检测页面变化
    function detectPageChange() {
        const url = window.location.href;
        
        if (url.includes('personal-info')) {
            updateStatus('检测到个人信息页面', 'info');
        } else if (url.includes('account-info')) {
            updateStatus('检测到账户信息页面', 'info');
        } else if (url.includes('contact-info')) {
            updateStatus('检测到联系信息页面', 'info');
        }
    }

    // 监听页面变化
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            detectPageChange();
        }
    }).observe(document, { subtree: true, childList: true });

    // 初始化
    function init() {
        // 等待页面完全加载
        setTimeout(() => {
            createControlPanel();
            detectPageChange();
            updateStatus('脚本已加载，准备开始批量处理', 'success');
        }, 1000);
    }

    // 启动脚本
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
