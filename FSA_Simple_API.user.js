// ==UserScript==
// @name         FSA 注册检查器 - 简化版
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  简化版FSA注册状态检查器，只检查是否已注册
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_xmlhttpRequest
// @grant        GM_download
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 您的学生数据
    const STUDENTS = [
        { name: 'STEPHANIE', mi: 'A', last: 'STILLINGS', dob: '07/18/1990', ssn: '*********' },
        { name: 'AARON', mi: 'D', last: 'CORNETT', dob: '01/18/1990', ssn: '*********' },
        { name: 'MORGAN', mi: 'C', last: 'STEPHENS', dob: '04/24/1997', ssn: '*********' }
    ];

    let results = [];

    // API调用函数
    function apiCall(endpoint, data) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `https://studentaid.gov/app/api/auth/registration/${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: JSON.stringify(data),
                timeout: 30000,
                onload: (response) => {
                    try {
                        resolve({
                            status: response.status,
                            data: response.responseText ? JSON.parse(response.responseText) : null
                        });
                    } catch (e) {
                        resolve({ status: response.status, data: response.responseText });
                    }
                },
                onerror: reject,
                ontimeout: () => reject(new Error('超时'))
            });
        });
    }

    // 检查单个学生注册状态
    async function checkStudent(student) {
        const [month, day, year] = student.dob.split('/');
        const fullName = `${student.name} ${student.mi} ${student.last}`;

        console.log(`🔍 检查: ${fullName}`);
        updateStatus(`检查: ${fullName}`);

        try {
            // 步骤1: 检查出生日期
            const dobResult = await apiCall('checkDob', { month, day, year });
            if (dobResult.status !== 200) {
                return { name: fullName, registered: false, message: `出生日期验证失败: ${dobResult.status}` };
            }

            // 步骤2: 尝试提交个人信息来检查注册状态
            const personalData = {
                firstName: student.name,
                middleInitial: student.mi,
                lastName: student.last,
                birthMonth: month,
                birthDay: day,
                birthYear: year,
                ssn: student.ssn
            };

            const personalResult = await apiCall('a', personalData);

            if (personalResult.status === 409) {
                // 409 = 冲突，账户已存在
                return { name: fullName, registered: true, message: '账户已注册' };
            } else if (personalResult.status === 200) {
                // 200 = 成功，可以注册
                return { name: fullName, registered: false, message: '可以注册' };
            } else {
                return { name: fullName, registered: false, message: `检查失败: ${personalResult.status}` };
            }

        } catch (error) {
            return { name: fullName, registered: false, message: `检查异常: ${error.message}` };
        }
    }

    // 批量检查
    async function checkBatch() {
        updateStatus('开始批量检查注册状态...');
        results = [];

        for (let i = 0; i < STUDENTS.length; i++) {
            const student = STUDENTS[i];
            updateProgress(i + 1, STUDENTS.length);

            const result = await checkStudent(student);
            results.push(result);

            if (result.registered) {
                console.log(`✅ ${result.name} - 已注册`);
                updateStatus(`✅ ${result.name} - 已注册`);
            } else {
                console.log(`🆕 ${result.name} - ${result.message}`);
                updateStatus(`🆕 ${result.name} - ${result.message}`);
            }

            // 延迟2-4秒
            if (i < STUDENTS.length - 1) {
                const delay = 2000 + Math.random() * 2000;
                updateStatus(`等待 ${Math.round(delay/1000)} 秒...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        showResults();
    }

    // 显示检查结果
    function showResults() {
        const registered = results.filter(r => r.registered).length;
        const available = results.filter(r => !r.registered && r.message.includes('可以注册')).length;
        const total = results.length;

        updateStatus(`检查完成: ${registered} 已注册, ${available} 可注册`);

        let report = `=== FSA 注册状态检查结果 ===\n`;
        report += `检查时间: ${new Date().toLocaleString()}\n`;
        report += `总计: ${total} | 已注册: ${registered} | 可注册: ${available}\n\n`;

        results.forEach((result, index) => {
            report += `${index + 1}. ${result.name}\n`;
            report += `   状态: ${result.registered ? '✅ 已注册' : '🆕 可注册'}\n`;
            report += `   详情: ${result.message}\n\n`;
        });

        // 下载报告
        const blob = new Blob([report], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
        GM_download(url, `FSA_注册检查结果_${timestamp}.txt`, url);

        console.log(report);
        alert(`检查完成！\n已注册: ${registered}/${total}\n可注册: ${available}/${total}\n报告已自动下载`);
    }

    // 创建控制界面
    function createUI() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed; top: 20px; right: 20px; width: 300px;
            background: white; border: 2px solid #007bff; border-radius: 8px;
            padding: 15px; z-index: 10000; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif; font-size: 14px;
        `;

        panel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #007bff;">� FSA 注册检查器</h4>
            <div style="margin-bottom: 10px;">
                <strong>学生数量:</strong> ${STUDENTS.length}<br>
                <strong>功能:</strong> 检查注册状态
            </div>
            <button id="start-process" style="
                background: #007bff; color: white; border: none;
                padding: 10px 20px; border-radius: 4px; cursor: pointer; width: 100%;
            ">开始检查注册状态</button>
            <div id="status" style="margin-top: 10px; font-size: 12px; color: #666;">
                准备就绪
            </div>
            <div id="progress" style="margin-top: 5px; font-size: 12px; color: #666;">
                0/${STUDENTS.length}
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('start-process').addEventListener('click', async function() {
            this.disabled = true;
            this.textContent = '检查中...';
            await checkBatch();
            this.disabled = false;
            this.textContent = '重新检查';
        });
    }

    // 更新状态
    function updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) statusEl.textContent = message;
    }

    // 更新进度
    function updateProgress(current, total) {
        const progressEl = document.getElementById('progress');
        if (progressEl) progressEl.textContent = `${current}/${total}`;
    }

    // 初始化
    setTimeout(() => {
        createUI();
        console.log('FSA 注册检查器已加载');
        console.log('学生数据:', STUDENTS);
    }, 1000);

})();
