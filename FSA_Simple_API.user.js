// ==UserScript==
// @name         FSA 简化API处理器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  简化版FSA API批量处理，直接后台调用
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_xmlhttpRequest
// @grant        GM_download
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 您的学生数据
    const STUDENTS = [
        { name: 'STEPHANIE', mi: 'A', last: 'STILLINGS', dob: '07/18/1990', ssn: '*********' },
        { name: 'AARON', mi: 'D', last: 'CORNETT', dob: '01/18/1990', ssn: '*********' },
        { name: 'MORGAN', mi: 'C', last: 'STEPHENS', dob: '04/24/1997', ssn: '*********' }
    ];

    let results = [];

    // API调用函数
    function apiCall(endpoint, data) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: `https://studentaid.gov/app/api/auth/registration/${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: JSON.stringify(data),
                timeout: 30000,
                onload: (response) => {
                    try {
                        resolve({
                            status: response.status,
                            data: response.responseText ? JSON.parse(response.responseText) : null
                        });
                    } catch (e) {
                        resolve({ status: response.status, data: response.responseText });
                    }
                },
                onerror: reject,
                ontimeout: () => reject(new Error('超时'))
            });
        });
    }

    // 处理单个学生
    async function processStudent(student) {
        const [month, day, year] = student.dob.split('/');
        const fullName = `${student.name} ${student.mi} ${student.last}`;
        
        console.log(`🔄 处理: ${fullName}`);
        updateStatus(`处理: ${fullName}`);

        try {
            // 步骤1: 检查出生日期
            const dobResult = await apiCall('checkDob', { month, day, year });
            if (dobResult.status !== 200) {
                return { name: fullName, success: false, message: `出生日期验证失败: ${dobResult.status}` };
            }

            // 步骤2: 提交个人信息
            const personalData = {
                firstName: student.name,
                middleInitial: student.mi,
                lastName: student.last,
                birthMonth: month,
                birthDay: day,
                birthYear: year,
                ssn: student.ssn
            };

            const personalResult = await apiCall('a', personalData);
            
            if (personalResult.status === 409) {
                return { name: fullName, success: false, message: '账户已存在' };
            } else if (personalResult.status !== 200) {
                return { name: fullName, success: false, message: `个人信息提交失败: ${personalResult.status}` };
            }

            // 等待2秒
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 步骤3: 创建账户信息
            const username = `${student.name.toLowerCase()}.${student.last.toLowerCase()}${student.ssn.slice(-4)}`;
            const email = `${username}@gmail.com`;
            const password = generatePassword();

            const accountData = {
                username,
                email,
                confirmEmail: email,
                password,
                confirmPassword: password
            };

            const accountResult = await apiCall('b', accountData);
            
            if (accountResult.status === 200) {
                return {
                    name: fullName,
                    success: true,
                    message: '账户创建成功',
                    credentials: { username, email, password }
                };
            } else {
                return { name: fullName, success: false, message: `账户创建失败: ${accountResult.status}` };
            }

        } catch (error) {
            return { name: fullName, success: false, message: `处理异常: ${error.message}` };
        }
    }

    // 生成密码
    function generatePassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = 'A1a'; // 确保包含大写、小写、数字
        for (let i = 0; i < 5; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    // 批量处理
    async function processBatch() {
        updateStatus('开始批量处理...');
        results = [];

        for (let i = 0; i < STUDENTS.length; i++) {
            const student = STUDENTS[i];
            updateProgress(i + 1, STUDENTS.length);
            
            const result = await processStudent(student);
            results.push(result);
            
            if (result.success) {
                console.log(`✅ ${result.name} - 成功`);
                updateStatus(`✅ ${result.name} - 成功`);
            } else {
                console.log(`❌ ${result.name} - ${result.message}`);
                updateStatus(`❌ ${result.name} - ${result.message}`);
            }

            // 延迟3-5秒
            if (i < STUDENTS.length - 1) {
                const delay = 3000 + Math.random() * 2000;
                updateStatus(`等待 ${Math.round(delay/1000)} 秒...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        showResults();
    }

    // 显示结果
    function showResults() {
        const successful = results.filter(r => r.success).length;
        const total = results.length;
        
        updateStatus(`处理完成: ${successful}/${total} 成功`);
        
        let report = `=== FSA API 处理结果 ===\n`;
        report += `成功: ${successful}/${total}\n\n`;
        
        results.forEach((result, index) => {
            report += `${index + 1}. ${result.name}\n`;
            report += `   状态: ${result.success ? '✅ 成功' : '❌ 失败'}\n`;
            report += `   信息: ${result.message}\n`;
            
            if (result.success && result.credentials) {
                report += `   用户名: ${result.credentials.username}\n`;
                report += `   邮箱: ${result.credentials.email}\n`;
                report += `   密码: ${result.credentials.password}\n`;
            }
            report += '\n';
        });

        // 下载报告
        const blob = new Blob([report], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
        GM_download(url, `FSA_处理结果_${timestamp}.txt`, url);
        
        console.log(report);
        alert(`处理完成！\n成功: ${successful}/${total}\n报告已自动下载`);
    }

    // 创建控制界面
    function createUI() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed; top: 20px; right: 20px; width: 300px;
            background: white; border: 2px solid #007bff; border-radius: 8px;
            padding: 15px; z-index: 10000; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif; font-size: 14px;
        `;

        panel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #007bff;">🚀 FSA API 处理器</h4>
            <div style="margin-bottom: 10px;">
                <strong>学生数量:</strong> ${STUDENTS.length}<br>
                <strong>模式:</strong> 纯API调用
            </div>
            <button id="start-process" style="
                background: #28a745; color: white; border: none; 
                padding: 10px 20px; border-radius: 4px; cursor: pointer; width: 100%;
            ">开始批量处理</button>
            <div id="status" style="margin-top: 10px; font-size: 12px; color: #666;">
                准备就绪
            </div>
            <div id="progress" style="margin-top: 5px; font-size: 12px; color: #666;">
                0/${STUDENTS.length}
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('start-process').addEventListener('click', async function() {
            this.disabled = true;
            this.textContent = '处理中...';
            await processBatch();
            this.disabled = false;
            this.textContent = '重新处理';
        });
    }

    // 更新状态
    function updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) statusEl.textContent = message;
    }

    // 更新进度
    function updateProgress(current, total) {
        const progressEl = document.getElementById('progress');
        if (progressEl) progressEl.textContent = `${current}/${total}`;
    }

    // 初始化
    setTimeout(() => {
        createUI();
        console.log('FSA API 处理器已加载');
        console.log('学生数据:', STUDENTS);
    }, 1000);

})();
