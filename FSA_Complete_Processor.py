#!/usr/bin/env python3
"""
FSA Complete Account Processor - 完整账户创建流程
支持从个人信息到账户信息的完整创建流程
"""

import requests
import json
import time
import random
import string
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging
import hashlib

class FSACompleteProcessor:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://studentaid.gov"
        self.results = []
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'Referer': 'https://studentaid.gov/fsa-id/create-account/',
            'Origin': 'https://studentaid.gov'
        })

    def generate_username(self, first_name: str, last_name: str, ssn: str) -> str:
        """生成用户名"""
        # 使用姓名和SSN的组合生成唯一用户名
        base = f"{first_name.lower()}.{last_name.lower()}"
        suffix = ssn[-4:]  # 使用SSN后4位
        return f"{base}{suffix}"

    def generate_email(self, first_name: str, last_name: str, ssn: str) -> str:
        """生成邮箱地址"""
        username = self.generate_username(first_name, last_name, ssn)
        # 使用常见的邮箱服务商
        domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
        domain = random.choice(domains)
        return f"{username}@{domain}"

    def generate_password(self) -> str:
        """生成符合要求的密码"""
        # 密码要求：大写字母、小写字母、数字、8+字符
        password = ""
        password += random.choice(string.ascii_uppercase)  # 大写字母
        password += random.choice(string.ascii_lowercase)  # 小写字母
        password += random.choice(string.digits)  # 数字
        
        # 添加更多字符到8位
        remaining = 8 - len(password)
        chars = string.ascii_letters + string.digits
        password += ''.join(random.choice(chars) for _ in range(remaining))
        
        # 打乱顺序
        password_list = list(password)
        random.shuffle(password_list)
        return ''.join(password_list)

    def step1_personal_info(self, student_data: Dict) -> Tuple[bool, str, Dict]:
        """步骤1：提交个人信息"""
        try:
            personal_data = {
                "firstName": student_data["first_name"],
                "middleInitial": student_data["middle_initial"],
                "lastName": student_data["last_name"],
                "birthMonth": student_data["birth_month"],
                "birthDay": student_data["birth_day"],
                "birthYear": student_data["birth_year"],
                "ssn": student_data["ssn"]
            }
            
            # 先检查出生日期
            dob_response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/checkDob",
                json={
                    "month": student_data["birth_month"],
                    "day": student_data["birth_day"],
                    "year": student_data["birth_year"]
                }
            )
            
            if dob_response.status_code != 200:
                return False, f"出生日期验证失败: {dob_response.status_code}", {}
            
            # 提交个人信息
            response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/a",
                json=personal_data
            )
            
            if response.status_code == 200:
                return True, "个人信息提交成功", response.json()
            elif response.status_code == 409:
                return False, "账户已存在", {}
            else:
                return False, f"个人信息提交失败: {response.status_code}", {}
                
        except Exception as e:
            return False, f"个人信息提交异常: {str(e)}", {}

    def step2_account_info(self, student_data: Dict) -> Tuple[bool, str, Dict]:
        """步骤2：提交账户信息"""
        try:
            # 生成账户信息
            username = self.generate_username(
                student_data["first_name"], 
                student_data["last_name"], 
                student_data["ssn"]
            )
            email = self.generate_email(
                student_data["first_name"], 
                student_data["last_name"], 
                student_data["ssn"]
            )
            password = self.generate_password()
            
            account_data = {
                "username": username,
                "email": email,
                "confirmEmail": email,
                "password": password,
                "confirmPassword": password
            }
            
            # 提交账户信息
            response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/b",
                json=account_data
            )
            
            if response.status_code == 200:
                result = response.json()
                result.update({
                    "generated_username": username,
                    "generated_email": email,
                    "generated_password": password
                })
                return True, "账户信息提交成功", result
            else:
                return False, f"账户信息提交失败: {response.status_code}", {}
                
        except Exception as e:
            return False, f"账户信息提交异常: {str(e)}", {}

    def step3_contact_info(self, student_data: Dict) -> Tuple[bool, str, Dict]:
        """步骤3：提交联系信息（可选）"""
        try:
            # 生成虚拟联系信息
            contact_data = {
                "address1": "123 Main Street",
                "city": "Anytown",
                "state": "CA",
                "zipCode": "90210",
                "phoneNumber": f"555{random.randint(1000000, 9999999)}"
            }
            
            response = self.session.post(
                f"{self.base_url}/app/api/auth/registration/c",
                json=contact_data
            )
            
            if response.status_code == 200:
                return True, "联系信息提交成功", response.json()
            else:
                return False, f"联系信息提交失败: {response.status_code}", {}
                
        except Exception as e:
            return False, f"联系信息提交异常: {str(e)}", {}

    def process_complete_account(self, student_data: Dict) -> Dict:
        """完整的账户创建流程"""
        start_time = datetime.now()
        result = {
            "student_name": f"{student_data['first_name']} {student_data['middle_initial']} {student_data['last_name']}",
            "ssn": student_data["ssn"],
            "start_time": start_time.isoformat(),
            "steps_completed": [],
            "success": False,
            "final_message": "",
            "account_details": {},
            "errors": []
        }
        
        try:
            logging.info(f"开始完整账户创建: {result['student_name']}")
            
            # 步骤1：个人信息
            step1_success, step1_msg, step1_data = self.step1_personal_info(student_data)
            result["steps_completed"].append({
                "step": "1_personal_info",
                "success": step1_success,
                "message": step1_msg,
                "data": step1_data
            })
            
            if not step1_success:
                result["final_message"] = f"步骤1失败: {step1_msg}"
                if "已存在" in step1_msg:
                    result["final_message"] = "账户已存在，跳过创建"
                return result
            
            # 等待一下再进行下一步
            time.sleep(random.uniform(1, 3))
            
            # 步骤2：账户信息
            step2_success, step2_msg, step2_data = self.step2_account_info(student_data)
            result["steps_completed"].append({
                "step": "2_account_info", 
                "success": step2_success,
                "message": step2_msg,
                "data": step2_data
            })
            
            if step2_success:
                result["account_details"] = {
                    "username": step2_data.get("generated_username"),
                    "email": step2_data.get("generated_email"),
                    "password": step2_data.get("generated_password")
                }
                result["success"] = True
                result["final_message"] = "账户创建成功！已完成个人信息和账户信息步骤"
                logging.info(f"✅ 账户创建成功: {result['student_name']}")
            else:
                result["final_message"] = f"步骤2失败: {step2_msg}"
                logging.error(f"❌ 步骤2失败: {result['student_name']} - {step2_msg}")
            
        except Exception as e:
            result["final_message"] = f"处理过程中出现异常: {str(e)}"
            result["errors"].append(str(e))
            logging.error(f"处理异常: {result['student_name']} - {str(e)}")
        
        finally:
            end_time = datetime.now()
            result["end_time"] = end_time.isoformat()
            result["duration"] = str(end_time - start_time)
        
        return result

    def process_batch_complete(self, students_data: List[Dict]) -> List[Dict]:
        """批量完整账户创建"""
        logging.info(f"开始批量完整账户创建: {len(students_data)} 个学生")
        
        # 初始化会话
        try:
            init_response = self.session.get(f"{self.base_url}/fsa-id/create-account/personal-info")
            if init_response.status_code != 200:
                logging.error("会话初始化失败")
                return []
        except Exception as e:
            logging.error(f"会话初始化异常: {str(e)}")
            return []
        
        results = []
        
        for i, student_data in enumerate(students_data, 1):
            logging.info(f"处理进度: {i}/{len(students_data)}")
            
            # 处理单个学生的完整账户创建
            result = self.process_complete_account(student_data)
            results.append(result)
            
            # 添加延迟
            if i < len(students_data):
                delay = random.randint(3, 8)
                logging.info(f"等待 {delay} 秒后处理下一个学生...")
                time.sleep(delay)
        
        self.results = results
        return results

    def generate_detailed_report(self) -> str:
        """生成详细报告"""
        if not self.results:
            return "没有处理结果"
        
        total = len(self.results)
        successful = sum(1 for r in self.results if r["success"])
        
        report = f"""
=== FSA 完整账户创建报告 ===
处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总计学生: {total}
成功创建: {successful}
失败/跳过: {total - successful}
成功率: {(successful/total*100):.1f}%

=== 成功创建的账户信息 ===
"""
        
        for i, result in enumerate(self.results, 1):
            report += f"\n{i}. {result['student_name']} (SSN: {result['ssn']})\n"
            report += f"   状态: {'✅ 成功' if result['success'] else '❌ 失败'}\n"
            report += f"   最终信息: {result['final_message']}\n"
            
            if result["success"] and result["account_details"]:
                details = result["account_details"]
                report += f"   用户名: {details.get('username', 'N/A')}\n"
                report += f"   邮箱: {details.get('email', 'N/A')}\n"
                report += f"   密码: {details.get('password', 'N/A')}\n"
            
            report += f"   耗时: {result['duration']}\n"
            
            # 显示步骤详情
            for step in result["steps_completed"]:
                status = "✅" if step["success"] else "❌"
                report += f"     {step['step']}: {status} {step['message']}\n"
        
        return report

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('fsa_complete_processor.log'),
            logging.StreamHandler()
        ]
    )
    
    # 学生数据
    students_data = [
        {
            "first_name": "STEPHANIE",
            "middle_initial": "A", 
            "last_name": "STILLINGS",
            "birth_month": "07",
            "birth_day": "18",
            "birth_year": "1990",
            "ssn": "491040179"
        },
        {
            "first_name": "AARON",
            "middle_initial": "D",
            "last_name": "CORNETT", 
            "birth_month": "01",
            "birth_day": "18",
            "birth_year": "1990",
            "ssn": "371116023"
        },
        {
            "first_name": "MORGAN",
            "middle_initial": "C",
            "last_name": "STEPHENS",
            "birth_month": "04", 
            "birth_day": "24",
            "birth_year": "1997",
            "ssn": "002908966"
        }
    ]
    
    # 创建处理器
    processor = FSACompleteProcessor()
    
    # 批量处理
    results = processor.process_batch_complete(students_data)
    
    # 生成报告
    report = processor.generate_detailed_report()
    print(report)
    
    # 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"FSA_完整处理报告_{timestamp}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 保存账户信息到JSON
    accounts_file = f"FSA_账户信息_{timestamp}.json"
    with open(accounts_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logging.info(f"处理完成！报告: {report_file}, 账户信息: {accounts_file}")
    
    return results

if __name__ == "__main__":
    main()
