// ==UserScript==
// @name         FSA 安全检查器 - 防403版本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  更安全的FSA注册状态检查器，防止403错误
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_xmlhttpRequest
// @grant        GM_download
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 学生数据
    const STUDENTS = [
        { name: 'STEPHANIE', mi: 'A', last: 'STILLINGS', dob: '07/18/1990', ssn: '*********' },
        { name: 'AARON', mi: 'D', last: 'CORNETT', dob: '01/18/1990', ssn: '*********' },
        { name: 'MORGAN', mi: 'C', last: 'STEPHENS', dob: '04/24/1997', ssn: '*********' }
    ];

    let checkResults = [];
    let isChecking = false;

    // 安全的API调用函数 - 带重试机制
    async function safeApiCall(endpoint, data, retries = 3) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                console.log(`尝试 ${attempt}/${retries}: ${endpoint}`);
                
                const result = await new Promise((resolve, reject) => {
                    // 获取当前页面的所有cookies
                    const cookies = document.cookie;
                    
                    const headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': window.location.href,
                        'Origin': 'https://studentaid.gov',
                        'User-Agent': navigator.userAgent,
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin'
                    };

                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: `https://studentaid.gov/app/api/auth/registration/${endpoint}`,
                        headers: headers,
                        data: JSON.stringify(data),
                        timeout: 30000,
                        onload: (response) => {
                            resolve({
                                status: response.status,
                                statusText: response.statusText,
                                data: response.responseText
                            });
                        },
                        onerror: reject,
                        ontimeout: () => reject(new Error('请求超时'))
                    });
                });

                // 如果成功，返回结果
                if (result.status !== 403) {
                    return result;
                }

                // 如果是403，等待更长时间再重试
                if (attempt < retries) {
                    const waitTime = attempt * 10000; // 10秒, 20秒, 30秒
                    console.log(`收到403错误，等待 ${waitTime/1000} 秒后重试...`);
                    updateStatus(`收到403错误，等待 ${waitTime/1000} 秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }

            } catch (error) {
                console.error(`尝试 ${attempt} 失败:`, error);
                if (attempt === retries) {
                    throw error;
                }
                
                // 等待后重试
                const waitTime = attempt * 5000;
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }

        throw new Error('所有重试都失败了');
    }

    // 检查单个学生 - 更保守的方法
    async function checkStudentSafely(student) {
        const [month, day, year] = student.dob.split('/');
        const fullName = `${student.name} ${student.mi} ${student.last}`;
        
        console.log(`🔍 安全检查: ${fullName}`);
        updateStatus(`安全检查: ${fullName}`);

        const result = {
            name: fullName,
            ssn: student.ssn,
            dob: student.dob,
            registered: false,
            status: '',
            message: '',
            checkTime: new Date().toLocaleString()
        };

        try {
            // 步骤1: 检查出生日期 - 带重试
            updateStatus(`${fullName} - 验证出生日期...`);
            const dobData = { month, day, year };
            const dobResult = await safeApiCall('checkDob', dobData);
            
            if (dobResult.status !== 200) {
                result.status = 'DOB_ERROR';
                result.message = `出生日期验证失败: HTTP ${dobResult.status}`;
                return result;
            }

            // 等待5-8秒
            const delay1 = 5000 + Math.random() * 3000;
            updateStatus(`${fullName} - 等待 ${Math.round(delay1/1000)} 秒...`);
            await new Promise(resolve => setTimeout(resolve, delay1));

            // 步骤2: 检查个人信息 - 带重试
            updateStatus(`${fullName} - 检查注册状态...`);
            const personalData = {
                firstName: student.name,
                middleInitial: student.mi,
                lastName: student.last,
                birthMonth: month,
                birthDay: day,
                birthYear: year,
                ssn: student.ssn
            };

            const personalResult = await safeApiCall('a', personalData);
            
            if (personalResult.status === 409) {
                result.registered = true;
                result.status = 'REGISTERED';
                result.message = '账户已注册';
            } else if (personalResult.status === 200) {
                result.registered = false;
                result.status = 'AVAILABLE';
                result.message = '可以注册';
            } else {
                result.status = 'UNKNOWN';
                result.message = `未知状态: HTTP ${personalResult.status}`;
            }

        } catch (error) {
            result.status = 'ERROR';
            result.message = `检查异常: ${error.message}`;
        }

        return result;
    }

    // 批量检查 - 超级保守模式
    async function checkAllStudentsSafely() {
        if (isChecking) return;
        
        isChecking = true;
        checkResults = [];
        
        const checkBtn = document.getElementById('safe-check-btn');
        const downloadBtn = document.getElementById('download-btn');
        
        checkBtn.disabled = true;
        checkBtn.textContent = '安全检查中...';
        downloadBtn.disabled = true;

        updateStatus('开始安全模式批量检查...');

        try {
            for (let i = 0; i < STUDENTS.length; i++) {
                const student = STUDENTS[i];
                updateProgress(i + 1, STUDENTS.length);
                
                updateStatus(`准备检查第 ${i + 1} 个学生...`);
                
                const result = await checkStudentSafely(student);
                checkResults.push(result);
                
                // 显示结果
                if (result.registered) {
                    console.log(`✅ ${result.name} - 已注册`);
                    updateStatus(`✅ ${result.name} - 已注册`);
                } else if (result.status === 'AVAILABLE') {
                    console.log(`🆕 ${result.name} - 可注册`);
                    updateStatus(`🆕 ${result.name} - 可注册`);
                } else {
                    console.log(`❌ ${result.name} - ${result.message}`);
                    updateStatus(`❌ ${result.name} - ${result.message}`);
                }

                // 更新结果显示
                updateResultsDisplay();

                // 超长延迟（除了最后一个）
                if (i < STUDENTS.length - 1) {
                    const delay = 15000 + Math.random() * 10000; // 15-25秒随机延迟
                    updateStatus(`安全延迟 ${Math.round(delay/1000)} 秒后检查下一个...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }

            updateStatus('🎉 安全检查完成！');
            
        } catch (error) {
            updateStatus(`❌ 安全检查出错: ${error.message}`);
        } finally {
            isChecking = false;
            checkBtn.disabled = false;
            checkBtn.textContent = '重新安全检查';
            downloadBtn.disabled = false;
        }
    }

    // 创建安全版控制界面
    function createSafeUI() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed; top: 20px; right: 20px; width: 320px;
            background: white; border: 2px solid #dc3545; border-radius: 8px;
            padding: 15px; z-index: 10000; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif; font-size: 14px; max-height: 80vh; overflow-y: auto;
        `;

        panel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #dc3545;">🛡️ FSA 安全检查器</h4>
            <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                <strong>模式:</strong> 超级安全模式<br>
                <strong>特点:</strong> 防403错误，自动重试<br>
                <strong>学生数量:</strong> ${STUDENTS.length}<br>
                <strong>预计时间:</strong> 约3-5分钟
            </div>
            <button id="safe-check-btn" style="
                background: #dc3545; color: white; border: none; 
                padding: 10px 20px; border-radius: 4px; cursor: pointer; width: 100%; margin-bottom: 10px;
            ">开始安全检查</button>
            <button id="download-btn" style="
                background: #28a745; color: white; border: none; 
                padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%;
            " disabled>下载检查结果</button>
            <div id="status" style="margin-top: 10px; font-size: 12px; color: #666;">
                安全模式就绪 - 防403错误
            </div>
            <div id="progress" style="margin-top: 5px; font-size: 12px; color: #666;">
                0/${STUDENTS.length}
            </div>
            <div id="results" style="margin-top: 10px; font-size: 12px; border-top: 1px solid #eee; padding-top: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">检查结果:</div>
                <div id="results-content">等待开始安全检查...</div>
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('safe-check-btn').addEventListener('click', checkAllStudentsSafely);
        document.getElementById('download-btn').addEventListener('click', downloadResults);
    }

    // 更新状态显示
    function updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) statusEl.textContent = message;
        console.log(`[FSA Safe] ${message}`);
    }

    // 更新进度显示
    function updateProgress(current, total) {
        const progressEl = document.getElementById('progress');
        if (progressEl) progressEl.textContent = `${current}/${total}`;
    }

    // 更新结果显示
    function updateResultsDisplay() {
        const resultsEl = document.getElementById('results-content');
        if (!resultsEl || checkResults.length === 0) return;

        const registered = checkResults.filter(r => r.registered).length;
        const available = checkResults.filter(r => r.status === 'AVAILABLE').length;
        const errors = checkResults.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN' || r.status === 'DOB_ERROR').length;

        let html = `
            <div style="margin-bottom: 8px;">
                <span style="color: #28a745;">✅ 已注册: ${registered}</span> | 
                <span style="color: #007bff;">🆕 可注册: ${available}</span> | 
                <span style="color: #dc3545;">❌ 错误: ${errors}</span>
            </div>
        `;

        checkResults.forEach((result) => {
            let statusColor = '#666';
            let statusIcon = '❓';
            
            if (result.registered) {
                statusColor = '#28a745';
                statusIcon = '✅';
            } else if (result.status === 'AVAILABLE') {
                statusColor = '#007bff';
                statusIcon = '🆕';
            } else {
                statusColor = '#dc3545';
                statusIcon = '❌';
            }

            html += `
                <div style="margin-bottom: 5px; padding: 5px; background: #f8f9fa; border-radius: 3px;">
                    <div style="font-weight: bold; color: ${statusColor};">
                        ${statusIcon} ${result.name}
                    </div>
                    <div style="font-size: 11px; color: #666;">
                        ${result.message}
                    </div>
                </div>
            `;
        });

        resultsEl.innerHTML = html;
    }

    // 下载检查结果
    function downloadResults() {
        if (checkResults.length === 0) {
            alert('没有检查结果可下载');
            return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
        
        let report = `=== FSA 安全检查报告 ===\n`;
        report += `检查时间: ${new Date().toLocaleString()}\n`;
        report += `检查模式: 安全模式（防403错误）\n`;
        report += `总计学生: ${checkResults.length}\n`;
        
        const registered = checkResults.filter(r => r.registered).length;
        const available = checkResults.filter(r => r.status === 'AVAILABLE').length;
        const errors = checkResults.length - registered - available;
        
        report += `已注册: ${registered}\n`;
        report += `可注册: ${available}\n`;
        report += `检查失败: ${errors}\n\n`;
        
        report += `=== 详细结果 ===\n`;
        checkResults.forEach((result, index) => {
            report += `${index + 1}. ${result.name}\n`;
            report += `   SSN: ${result.ssn}\n`;
            report += `   状态: ${result.registered ? '已注册' : result.status === 'AVAILABLE' ? '可注册' : '检查失败'}\n`;
            report += `   详情: ${result.message}\n`;
            report += `   检查时间: ${result.checkTime}\n\n`;
        });

        const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        GM_download(url, `FSA_安全检查结果_${timestamp}.txt`, url);
        
        updateStatus('📁 安全检查结果已下载');
    }

    // 初始化
    setTimeout(() => {
        createSafeUI();
        console.log('FSA 安全检查器已加载 - 防403版本');
        console.log('安全特性: 自动重试、超长延迟、错误恢复');
        updateStatus('FSA 安全检查器已就绪 - 防403版本');
    }, 1000);

})();
